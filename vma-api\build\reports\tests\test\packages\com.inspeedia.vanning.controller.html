<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<meta http-equiv="x-ua-compatible" content="IE=edge"/>
<title>Test results - Package com.inspeedia.vanning.controller</title>
<link href="../css/base-style.css" rel="stylesheet" type="text/css"/>
<link href="../css/style.css" rel="stylesheet" type="text/css"/>
<script src="../js/report.js" type="text/javascript"></script>
</head>
<body>
<div id="content">
<h1>Package com.inspeedia.vanning.controller</h1>
<div class="breadcrumbs">
<a href="../index.html">all</a> &gt; com.inspeedia.vanning.controller</div>
<div id="summary">
<table>
<tr>
<td>
<div class="summaryGroup">
<table>
<tr>
<td>
<div class="infoBox" id="tests">
<div class="counter">4</div>
<p>tests</p>
</div>
</td>
<td>
<div class="infoBox" id="failures">
<div class="counter">4</div>
<p>failures</p>
</div>
</td>
<td>
<div class="infoBox" id="ignored">
<div class="counter">0</div>
<p>ignored</p>
</div>
</td>
<td>
<div class="infoBox" id="duration">
<div class="counter">0.088s</div>
<p>duration</p>
</div>
</td>
</tr>
</table>
</div>
</td>
<td>
<div class="infoBox failures" id="successRate">
<div class="percent">0%</div>
<p>successful</p>
</div>
</td>
</tr>
</table>
</div>
<div id="tabs">
<ul class="tabLinks">
<li>
<a href="#tab0">Failed tests</a>
</li>
<li>
<a href="#tab1">Classes</a>
</li>
</ul>
<div id="tab0" class="tab">
<h2>Failed tests</h2>
<ul class="linkList">
<li>
<a href="../classes/com.inspeedia.vanning.controller.TaskControllerTest.html">TaskControllerTest</a>.
<a href="../classes/com.inspeedia.vanning.controller.TaskControllerTest.html#testGetAllTasks()">testGetAllTasks()</a>
</li>
<li>
<a href="../classes/com.inspeedia.vanning.controller.TaskControllerTest.html">TaskControllerTest</a>.
<a href="../classes/com.inspeedia.vanning.controller.TaskControllerTest.html#testGetAllTasksEmpty()">testGetAllTasksEmpty()</a>
</li>
<li>
<a href="../classes/com.inspeedia.vanning.controller.TaskControllerTest.html">TaskControllerTest</a>.
<a href="../classes/com.inspeedia.vanning.controller.TaskControllerTest.html#testGetTodaysTasks()">testGetTodaysTasks()</a>
</li>
<li>
<a href="../classes/com.inspeedia.vanning.controller.TaskControllerTest.html">TaskControllerTest</a>.
<a href="../classes/com.inspeedia.vanning.controller.TaskControllerTest.html#testGetTodaysTasksEmpty()">testGetTodaysTasksEmpty()</a>
</li>
</ul>
</div>
<div id="tab1" class="tab">
<h2>Classes</h2>
<table>
<thead>
<tr>
<th>Class</th>
<th>Tests</th>
<th>Failures</th>
<th>Ignored</th>
<th>Duration</th>
<th>Success rate</th>
</tr>
</thead>
<tr>
<td class="failures">
<a href="../classes/com.inspeedia.vanning.controller.TaskControllerTest.html">TaskControllerTest</a>
</td>
<td>4</td>
<td>4</td>
<td>0</td>
<td>0.088s</td>
<td class="failures">0%</td>
</tr>
</table>
</div>
</div>
<div id="footer">
<p>
<div>
<label class="hidden" id="label-for-line-wrapping-toggle" for="line-wrapping-toggle">Wrap lines
<input id="line-wrapping-toggle" type="checkbox" autocomplete="off"/>
</label>
</div>Generated by 
<a href="http://www.gradle.org">Gradle 8.5</a> at Sep 1, 2025, 5:30:36 PM</p>
</div>
</div>
</body>
</html>

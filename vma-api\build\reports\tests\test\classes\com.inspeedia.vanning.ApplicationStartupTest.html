<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<meta http-equiv="x-ua-compatible" content="IE=edge"/>
<title>Test results - ApplicationStartupTest</title>
<link href="../css/base-style.css" rel="stylesheet" type="text/css"/>
<link href="../css/style.css" rel="stylesheet" type="text/css"/>
<script src="../js/report.js" type="text/javascript"></script>
</head>
<body>
<div id="content">
<h1>ApplicationStartupTest</h1>
<div class="breadcrumbs">
<a href="../index.html">all</a> &gt; 
<a href="../packages/com.inspeedia.vanning.html">com.inspeedia.vanning</a> &gt; ApplicationStartupTest</div>
<div id="summary">
<table>
<tr>
<td>
<div class="summaryGroup">
<table>
<tr>
<td>
<div class="infoBox" id="tests">
<div class="counter">1</div>
<p>tests</p>
</div>
</td>
<td>
<div class="infoBox" id="failures">
<div class="counter">0</div>
<p>failures</p>
</div>
</td>
<td>
<div class="infoBox" id="ignored">
<div class="counter">0</div>
<p>ignored</p>
</div>
</td>
<td>
<div class="infoBox" id="duration">
<div class="counter">1.068s</div>
<p>duration</p>
</div>
</td>
</tr>
</table>
</div>
</td>
<td>
<div class="infoBox success" id="successRate">
<div class="percent">100%</div>
<p>successful</p>
</div>
</td>
</tr>
</table>
</div>
<div id="tabs">
<ul class="tabLinks">
<li>
<a href="#tab0">Tests</a>
</li>
<li>
<a href="#tab1">Standard output</a>
</li>
</ul>
<div id="tab0" class="tab">
<h2>Tests</h2>
<table>
<thead>
<tr>
<th>Test</th>
<th>Duration</th>
<th>Result</th>
</tr>
</thead>
<tr>
<td class="success">contextLoads()</td>
<td class="success">1.068s</td>
<td class="success">passed</td>
</tr>
</table>
</div>
<div id="tab1" class="tab">
<h2>Standard output</h2>
<span class="code">
<pre>10:44:18.626 [Test worker] INFO org.springframework.test.context.support.AnnotationConfigContextLoaderUtils -- Could not detect default configuration classes for test class [com.inspeedia.vanning.ApplicationStartupTest]: ApplicationStartupTest does not declare any static, non-private, non-final, nested classes annotated with @Configuration.
10:44:18.880 [Test worker] INFO org.springframework.boot.test.context.SpringBootTestContextBootstrapper -- Found @SpringBootConfiguration com.inspeedia.vanning.VmApplication for test class com.inspeedia.vanning.ApplicationStartupTest

  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/

 :: Spring Boot ::                (v3.5.5)

{&quot;@timestamp&quot;:&quot;2025-09-02T10:44:20.2940497+05:30&quot;,&quot;level&quot;:&quot;INFO&quot;,&quot;logger_name&quot;:&quot;com.inspeedia.vanning.ApplicationStartupTest&quot;,&quot;message&quot;:&quot;Starting ApplicationStartupTest using Java 17.0.14 with PID 17836 (started by INSP_MSS in C:\\Dev\\Mohan\\Projects\\MSS\\Toyutsu\\vanning\\vma-api)&quot;}
{&quot;@timestamp&quot;:&quot;2025-09-02T10:44:20.3118252+05:30&quot;,&quot;level&quot;:&quot;INFO&quot;,&quot;logger_name&quot;:&quot;com.inspeedia.vanning.ApplicationStartupTest&quot;,&quot;message&quot;:&quot;The following 1 profile is active: \&quot;test\&quot;&quot;}
{&quot;@timestamp&quot;:&quot;2025-09-02T10:44:26.8469618+05:30&quot;,&quot;level&quot;:&quot;INFO&quot;,&quot;logger_name&quot;:&quot;com.inspeedia.vanning.config.SecurityConfig&quot;,&quot;message&quot;:&quot;Configured in-memory users: admin, user&quot;}
{&quot;@timestamp&quot;:&quot;2025-09-02T10:44:27.9808888+05:30&quot;,&quot;level&quot;:&quot;INFO&quot;,&quot;logger_name&quot;:&quot;com.inspeedia.vanning.config.WebConfig&quot;,&quot;message&quot;:&quot;CORS configured with origins: [*], methods: [*]&quot;}
{&quot;@timestamp&quot;:&quot;2025-09-02T10:44:29.3042214+05:30&quot;,&quot;level&quot;:&quot;INFO&quot;,&quot;logger_name&quot;:&quot;com.inspeedia.vanning.ApplicationStartupTest&quot;,&quot;message&quot;:&quot;Started ApplicationStartupTest in 10.173 seconds (process running for 12.617)&quot;}
</pre>
</span>
</div>
</div>
<div id="footer">
<p>
<div>
<label class="hidden" id="label-for-line-wrapping-toggle" for="line-wrapping-toggle">Wrap lines
<input id="line-wrapping-toggle" type="checkbox" autocomplete="off"/>
</label>
</div>Generated by 
<a href="http://www.gradle.org">Gradle 8.5</a> at Sep 2, 2025, 10:44:35 AM</p>
</div>
</div>
</body>
</html>

<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.inspeedia.vanning.service.PlannedWorkServiceTest" tests="14" skipped="0" failures="0" errors="0" timestamp="2025-09-01T12:10:47" hostname="MSI" time="0.144">
  <properties/>
  <testcase name="testUpdatePlannedWork()" classname="com.inspeedia.vanning.service.PlannedWorkServiceTest" time="0.103"/>
  <testcase name="testGetUpcomingPlannedWork()" classname="com.inspeedia.vanning.service.PlannedWorkServiceTest" time="0.003"/>
  <testcase name="testGetPlannedWorkByVanGp()" classname="com.inspeedia.vanning.service.PlannedWorkServiceTest" time="0.001"/>
  <testcase name="testDeletePlannedWork()" classname="com.inspeedia.vanning.service.PlannedWorkServiceTest" time="0.005"/>
  <testcase name="testGetAllActivePlannedWork()" classname="com.inspeedia.vanning.service.PlannedWorkServiceTest" time="0.003"/>
  <testcase name="testGetPlannedWorkById()" classname="com.inspeedia.vanning.service.PlannedWorkServiceTest" time="0.002"/>
  <testcase name="testCreatePlannedWork()" classname="com.inspeedia.vanning.service.PlannedWorkServiceTest" time="0.004"/>
  <testcase name="testDeletePlannedWorkNotFound()" classname="com.inspeedia.vanning.service.PlannedWorkServiceTest" time="0.003"/>
  <testcase name="testGetPlannedWorkByCollectionPlatform()" classname="com.inspeedia.vanning.service.PlannedWorkServiceTest" time="0.002"/>
  <testcase name="testGetPlannedWorkByDate()" classname="com.inspeedia.vanning.service.PlannedWorkServiceTest" time="0.002"/>
  <testcase name="testGetPlannedWorkBySize()" classname="com.inspeedia.vanning.service.PlannedWorkServiceTest" time="0.002"/>
  <testcase name="testUpdatePlannedWorkNotFound()" classname="com.inspeedia.vanning.service.PlannedWorkServiceTest" time="0.003"/>
  <testcase name="testGetPlannedWorkByDeliveryPlatform()" classname="com.inspeedia.vanning.service.PlannedWorkServiceTest" time="0.003"/>
  <testcase name="testGetPlannedWorkByIdNotFound()" classname="com.inspeedia.vanning.service.PlannedWorkServiceTest" time="0.002"/>
  <system-out><![CDATA[{"@timestamp":"2025-09-01T17:40:47.8666643+05:30","level":"INFO","logger_name":"com.inspeedia.vanning.service.PlannedWorkService","message":"Planned work updated successfully with ID: 1"}
{"@timestamp":"2025-09-01T17:40:47.8769386+05:30","level":"INFO","logger_name":"com.inspeedia.vanning.service.PlannedWorkService","message":"Planned work soft deleted successfully with ID: 1"}
{"@timestamp":"2025-09-01T17:40:47.8872667+05:30","level":"INFO","logger_name":"com.inspeedia.vanning.service.PlannedWorkService","message":"Planned work created successfully with ID: 1"}
{"@timestamp":"2025-09-01T17:40:47.8894121+05:30","level":"ERROR","logger_name":"com.inspeedia.vanning.service.PlannedWorkService","message":"Error deleting planned work: Planned work not found with ID: 999"}
{"@timestamp":"2025-09-01T17:40:47.9018581+05:30","level":"ERROR","logger_name":"com.inspeedia.vanning.service.PlannedWorkService","message":"Error updating planned work: Planned work not found with ID: 999"}
]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>

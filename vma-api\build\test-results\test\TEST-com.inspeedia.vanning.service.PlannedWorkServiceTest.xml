<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.inspeedia.vanning.service.PlannedWorkServiceTest" tests="14" skipped="0" failures="0" errors="0" timestamp="2025-09-02T05:14:34" hostname="MSI" time="0.2">
  <properties/>
  <testcase name="testUpdatePlannedWork()" classname="com.inspeedia.vanning.service.PlannedWorkServiceTest" time="0.145"/>
  <testcase name="testGetUpcomingPlannedWork()" classname="com.inspeedia.vanning.service.PlannedWorkServiceTest" time="0.003"/>
  <testcase name="testGetPlannedWorkByVanGp()" classname="com.inspeedia.vanning.service.PlannedWorkServiceTest" time="0.002"/>
  <testcase name="testDeletePlannedWork()" classname="com.inspeedia.vanning.service.PlannedWorkServiceTest" time="0.008"/>
  <testcase name="testGetAllActivePlannedWork()" classname="com.inspeedia.vanning.service.PlannedWorkServiceTest" time="0.005"/>
  <testcase name="testGetPlannedWorkById()" classname="com.inspeedia.vanning.service.PlannedWorkServiceTest" time="0.005"/>
  <testcase name="testCreatePlannedWork()" classname="com.inspeedia.vanning.service.PlannedWorkServiceTest" time="0.006"/>
  <testcase name="testDeletePlannedWorkNotFound()" classname="com.inspeedia.vanning.service.PlannedWorkServiceTest" time="0.003"/>
  <testcase name="testGetPlannedWorkByCollectionPlatform()" classname="com.inspeedia.vanning.service.PlannedWorkServiceTest" time="0.003"/>
  <testcase name="testGetPlannedWorkByDate()" classname="com.inspeedia.vanning.service.PlannedWorkServiceTest" time="0.002"/>
  <testcase name="testGetPlannedWorkBySize()" classname="com.inspeedia.vanning.service.PlannedWorkServiceTest" time="0.002"/>
  <testcase name="testUpdatePlannedWorkNotFound()" classname="com.inspeedia.vanning.service.PlannedWorkServiceTest" time="0.002"/>
  <testcase name="testGetPlannedWorkByDeliveryPlatform()" classname="com.inspeedia.vanning.service.PlannedWorkServiceTest" time="0.002"/>
  <testcase name="testGetPlannedWorkByIdNotFound()" classname="com.inspeedia.vanning.service.PlannedWorkServiceTest" time="0.003"/>
  <system-out><![CDATA[{"@timestamp":"2025-09-02T10:44:34.1482339+05:30","level":"INFO","logger_name":"com.inspeedia.vanning.service.PlannedWorkService","message":"Planned work updated successfully with ID: 1"}
{"@timestamp":"2025-09-02T10:44:34.163131+05:30","level":"INFO","logger_name":"com.inspeedia.vanning.service.PlannedWorkService","message":"Planned work soft deleted successfully with ID: 1"}
{"@timestamp":"2025-09-02T10:44:34.1809285+05:30","level":"INFO","logger_name":"com.inspeedia.vanning.service.PlannedWorkService","message":"Planned work created successfully with ID: 1"}
{"@timestamp":"2025-09-02T10:44:34.1838693+05:30","level":"ERROR","logger_name":"com.inspeedia.vanning.service.PlannedWorkService","message":"Error deleting planned work: Planned work not found with ID: 999"}
{"@timestamp":"2025-09-02T10:44:34.1980509+05:30","level":"ERROR","logger_name":"com.inspeedia.vanning.service.PlannedWorkService","message":"Error updating planned work: Planned work not found with ID: 999"}
]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>

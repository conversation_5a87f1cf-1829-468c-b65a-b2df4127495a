# Docker Profile Configuration
spring:
  r2dbc:
    url: r2dbc:mssql://${DB_HOST:mssql}:${DB_PORT:1433}/${DB_NAME:vma_db}
    username: ${DB_USERNAME:mssadmin}
    password: ${DB_PASSWORD:pass}
    pool:
      initial-size: 5
      max-size: 20
      max-idle-time: 30m

logging:
  level:
    root: INFO
    com.inspeedia.vanning: INFO
  file:
    name: /app/logs/vma-api.log

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus

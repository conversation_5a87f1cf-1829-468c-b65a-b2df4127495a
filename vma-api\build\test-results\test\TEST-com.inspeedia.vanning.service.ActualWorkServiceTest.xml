<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.inspeedia.vanning.service.ActualWorkServiceTest" tests="14" skipped="0" failures="0" errors="0" timestamp="2025-09-02T05:14:33" hostname="MSI" time="0.858">
  <properties/>
  <testcase name="testGetActualWorkByIdNotFound()" classname="com.inspeedia.vanning.service.ActualWorkServiceTest" time="0.716"/>
  <testcase name="testGetHighProgressWork()" classname="com.inspeedia.vanning.service.ActualWorkServiceTest" time="0.006"/>
  <testcase name="testGetActualWorkById()" classname="com.inspeedia.vanning.service.ActualWorkServiceTest" time="0.005"/>
  <testcase name="testUpdateActualWorkNotFound()" classname="com.inspeedia.vanning.service.ActualWorkServiceTest" time="0.029"/>
  <testcase name="testGetActualWorkByProgress()" classname="com.inspeedia.vanning.service.ActualWorkServiceTest" time="0.006"/>
  <testcase name="testGetAllActiveActualWork()" classname="com.inspeedia.vanning.service.ActualWorkServiceTest" time="0.021"/>
  <testcase name="testCreateActualWork()" classname="com.inspeedia.vanning.service.ActualWorkServiceTest" time="0.025"/>
  <testcase name="testCountActualWorkByUserName()" classname="com.inspeedia.vanning.service.ActualWorkServiceTest" time="0.006"/>
  <testcase name="testGetActualWorkByUserName()" classname="com.inspeedia.vanning.service.ActualWorkServiceTest" time="0.003"/>
  <testcase name="testDeleteActualWork()" classname="com.inspeedia.vanning.service.ActualWorkServiceTest" time="0.019"/>
  <testcase name="testUpdateActualWork()" classname="com.inspeedia.vanning.service.ActualWorkServiceTest" time="0.004"/>
  <testcase name="testGetActualWorkByStartTimeRange()" classname="com.inspeedia.vanning.service.ActualWorkServiceTest" time="0.003"/>
  <testcase name="testDeleteActualWorkNotFound()" classname="com.inspeedia.vanning.service.ActualWorkServiceTest" time="0.003"/>
  <testcase name="testGetLowProgressWork()" classname="com.inspeedia.vanning.service.ActualWorkServiceTest" time="0.002"/>
  <system-out><![CDATA[{"@timestamp":"2025-09-02T10:44:33.8961093+05:30","level":"ERROR","logger_name":"com.inspeedia.vanning.service.ActualWorkService","message":"Error updating actual work: Actual work not found with ID: 999"}
{"@timestamp":"2025-09-02T10:44:33.9501364+05:30","level":"INFO","logger_name":"com.inspeedia.vanning.service.ActualWorkService","message":"Actual work created successfully with ID: 1"}
{"@timestamp":"2025-09-02T10:44:33.9820294+05:30","level":"INFO","logger_name":"com.inspeedia.vanning.service.ActualWorkService","message":"Actual work soft deleted successfully with ID: 1"}
{"@timestamp":"2025-09-02T10:44:33.9869565+05:30","level":"INFO","logger_name":"com.inspeedia.vanning.service.ActualWorkService","message":"Actual work updated successfully with ID: 1"}
{"@timestamp":"2025-09-02T10:44:33.9928546+05:30","level":"ERROR","logger_name":"com.inspeedia.vanning.service.ActualWorkService","message":"Error deleting actual work: Actual work not found with ID: 999"}
]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>

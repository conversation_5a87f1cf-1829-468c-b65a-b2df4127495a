# Test Profile Configuration
spring:
  application:
    name: vma-api-test

  r2dbc:
    url: r2dbc:h2:mem:///testdb
    username: sa
    password: ""

  # Disable database initialization for tests
  sql:
    init:
      mode: never

# Override app properties for testing
app:
  cors:
    allowed-origins:
      - "*"
    allowed-methods:
      - "*"
    allowed-headers:
      - "*"
    allow-credentials: true
    max-age: 3600

  api:
    version: v1
    base-path: /api/v1

  pagination:
    default-page-size: 20
    max-page-size: 100

logging:
  level:
    root: WARN
    "[com.inspeedia.vanning]": INFO

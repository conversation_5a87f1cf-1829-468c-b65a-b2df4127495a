<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.inspeedia.vanning.VmApplicationTests" tests="1" skipped="0" failures="0" errors="0" timestamp="2025-09-02T05:14:31" hostname="MSI" time="0.007">
  <properties/>
  <testcase name="contextLoads()" classname="com.inspeedia.vanning.VmApplicationTests" time="0.007"/>
  <system-out><![CDATA[
  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/

 :: Spring Boot ::                (v3.5.5)

{"@timestamp":"2025-09-02T10:44:30.5040397+05:30","level":"INFO","logger_name":"com.inspeedia.vanning.VmApplicationTests","message":"Starting VmApplicationTests using Java 17.0.14 with PID 17836 (started by INSP_MSS in C:\\Dev\\Mohan\\Projects\\MSS\\Toyutsu\\vanning\\vma-api)"}
{"@timestamp":"2025-09-02T10:44:30.5040397+05:30","level":"INFO","logger_name":"com.inspeedia.vanning.VmApplicationTests","message":"The following 1 profile is active: \"test\""}
{"@timestamp":"2025-09-02T10:44:31.4558778+05:30","level":"INFO","logger_name":"com.inspeedia.vanning.config.SecurityConfig","message":"Configured in-memory users: admin, user"}
{"@timestamp":"2025-09-02T10:44:31.5820885+05:30","level":"INFO","logger_name":"com.inspeedia.vanning.config.WebConfig","message":"CORS configured with origins: [*], methods: [*]"}
{"@timestamp":"2025-09-02T10:44:31.8549702+05:30","level":"INFO","logger_name":"com.inspeedia.vanning.VmApplicationTests","message":"Started VmApplicationTests in 1.43 seconds (process running for 15.17)"}
]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>

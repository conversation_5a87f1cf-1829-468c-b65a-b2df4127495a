<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.inspeedia.vanning.VmApplicationTests" tests="1" skipped="0" failures="0" errors="0" timestamp="2025-09-01T12:10:46" hostname="MSI" time="0.007">
  <properties/>
  <testcase name="contextLoads()" classname="com.inspeedia.vanning.VmApplicationTests" time="0.007"/>
  <system-out><![CDATA[
  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/

 :: Spring Boot ::                (v3.5.5)

{"@timestamp":"2025-09-01T17:40:45.1652166+05:30","level":"INFO","logger_name":"com.inspeedia.vanning.VmApplicationTests","message":"Starting VmApplicationTests using Java 17.0.14 with PID 8456 (started by INSP_MSS in C:\\Dev\\Mohan\\Projects\\MSS\\Toyutsu\\vanning\\vma-api)"}
{"@timestamp":"2025-09-01T17:40:45.1661923+05:30","level":"INFO","logger_name":"com.inspeedia.vanning.VmApplicationTests","message":"The following 1 profile is active: \"test\""}
{"@timestamp":"2025-09-01T17:40:46.3715377+05:30","level":"INFO","logger_name":"com.inspeedia.vanning.config.SecurityConfig","message":"Configured in-memory users: admin, user"}
{"@timestamp":"2025-09-01T17:40:46.4770039+05:30","level":"INFO","logger_name":"com.inspeedia.vanning.config.WebConfig","message":"CORS configured with origins: [*], methods: [*]"}
{"@timestamp":"2025-09-01T17:40:46.8451454+05:30","level":"INFO","logger_name":"com.inspeedia.vanning.VmApplicationTests","message":"Started VmApplicationTests in 1.76 seconds (process running for 18.953)"}
]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>

<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<meta http-equiv="x-ua-compatible" content="IE=edge"/>
<title>Test results - ActualWorkServiceTest</title>
<link href="../css/base-style.css" rel="stylesheet" type="text/css"/>
<link href="../css/style.css" rel="stylesheet" type="text/css"/>
<script src="../js/report.js" type="text/javascript"></script>
</head>
<body>
<div id="content">
<h1>ActualWorkServiceTest</h1>
<div class="breadcrumbs">
<a href="../index.html">all</a> &gt; 
<a href="../packages/com.inspeedia.vanning.service.html">com.inspeedia.vanning.service</a> &gt; ActualWorkServiceTest</div>
<div id="summary">
<table>
<tr>
<td>
<div class="summaryGroup">
<table>
<tr>
<td>
<div class="infoBox" id="tests">
<div class="counter">14</div>
<p>tests</p>
</div>
</td>
<td>
<div class="infoBox" id="failures">
<div class="counter">0</div>
<p>failures</p>
</div>
</td>
<td>
<div class="infoBox" id="ignored">
<div class="counter">0</div>
<p>ignored</p>
</div>
</td>
<td>
<div class="infoBox" id="duration">
<div class="counter">0.848s</div>
<p>duration</p>
</div>
</td>
</tr>
</table>
</div>
</td>
<td>
<div class="infoBox success" id="successRate">
<div class="percent">100%</div>
<p>successful</p>
</div>
</td>
</tr>
</table>
</div>
<div id="tabs">
<ul class="tabLinks">
<li>
<a href="#tab0">Tests</a>
</li>
<li>
<a href="#tab1">Standard output</a>
</li>
</ul>
<div id="tab0" class="tab">
<h2>Tests</h2>
<table>
<thead>
<tr>
<th>Test</th>
<th>Duration</th>
<th>Result</th>
</tr>
</thead>
<tr>
<td class="success">testCountActualWorkByUserName()</td>
<td class="success">0.006s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testCreateActualWork()</td>
<td class="success">0.025s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testDeleteActualWork()</td>
<td class="success">0.019s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testDeleteActualWorkNotFound()</td>
<td class="success">0.003s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testGetActualWorkById()</td>
<td class="success">0.005s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testGetActualWorkByIdNotFound()</td>
<td class="success">0.716s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testGetActualWorkByProgress()</td>
<td class="success">0.006s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testGetActualWorkByStartTimeRange()</td>
<td class="success">0.003s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testGetActualWorkByUserName()</td>
<td class="success">0.003s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testGetAllActiveActualWork()</td>
<td class="success">0.021s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testGetHighProgressWork()</td>
<td class="success">0.006s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testGetLowProgressWork()</td>
<td class="success">0.002s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testUpdateActualWork()</td>
<td class="success">0.004s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testUpdateActualWorkNotFound()</td>
<td class="success">0.029s</td>
<td class="success">passed</td>
</tr>
</table>
</div>
<div id="tab1" class="tab">
<h2>Standard output</h2>
<span class="code">
<pre>{&quot;@timestamp&quot;:&quot;2025-09-02T10:44:33.8961093+05:30&quot;,&quot;level&quot;:&quot;ERROR&quot;,&quot;logger_name&quot;:&quot;com.inspeedia.vanning.service.ActualWorkService&quot;,&quot;message&quot;:&quot;Error updating actual work: Actual work not found with ID: 999&quot;}
{&quot;@timestamp&quot;:&quot;2025-09-02T10:44:33.9501364+05:30&quot;,&quot;level&quot;:&quot;INFO&quot;,&quot;logger_name&quot;:&quot;com.inspeedia.vanning.service.ActualWorkService&quot;,&quot;message&quot;:&quot;Actual work created successfully with ID: 1&quot;}
{&quot;@timestamp&quot;:&quot;2025-09-02T10:44:33.9820294+05:30&quot;,&quot;level&quot;:&quot;INFO&quot;,&quot;logger_name&quot;:&quot;com.inspeedia.vanning.service.ActualWorkService&quot;,&quot;message&quot;:&quot;Actual work soft deleted successfully with ID: 1&quot;}
{&quot;@timestamp&quot;:&quot;2025-09-02T10:44:33.9869565+05:30&quot;,&quot;level&quot;:&quot;INFO&quot;,&quot;logger_name&quot;:&quot;com.inspeedia.vanning.service.ActualWorkService&quot;,&quot;message&quot;:&quot;Actual work updated successfully with ID: 1&quot;}
{&quot;@timestamp&quot;:&quot;2025-09-02T10:44:33.9928546+05:30&quot;,&quot;level&quot;:&quot;ERROR&quot;,&quot;logger_name&quot;:&quot;com.inspeedia.vanning.service.ActualWorkService&quot;,&quot;message&quot;:&quot;Error deleting actual work: Actual work not found with ID: 999&quot;}
</pre>
</span>
</div>
</div>
<div id="footer">
<p>
<div>
<label class="hidden" id="label-for-line-wrapping-toggle" for="line-wrapping-toggle">Wrap lines
<input id="line-wrapping-toggle" type="checkbox" autocomplete="off"/>
</label>
</div>Generated by 
<a href="http://www.gradle.org">Gradle 8.5</a> at Sep 2, 2025, 10:44:35 AM</p>
</div>
</div>
</body>
</html>

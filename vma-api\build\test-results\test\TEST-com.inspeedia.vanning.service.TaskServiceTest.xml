<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.inspeedia.vanning.service.TaskServiceTest" tests="4" skipped="0" failures="0" errors="0" timestamp="2025-09-01T12:00:35" hostname="MSI" time="1.093">
  <properties/>
  <testcase name="testGetAllTasksWithoutActualWork()" classname="com.inspeedia.vanning.service.TaskServiceTest" time="1.041"/>
  <testcase name="testGetAllTasks()" classname="com.inspeedia.vanning.service.TaskServiceTest" time="0.014"/>
  <testcase name="testGetAllTasksEmpty()" classname="com.inspeedia.vanning.service.TaskServiceTest" time="0.017"/>
  <testcase name="testGetTodaysTasks()" classname="com.inspeedia.vanning.service.TaskServiceTest" time="0.016"/>
  <system-out><![CDATA[{"@timestamp":"2025-09-01T17:30:36.5939153+05:30","level":"DEBUG","logger_name":"com.inspeedia.vanning.service.TaskService","message":"Fetching all tasks by combining planned and actual work"}
{"@timestamp":"2025-09-01T17:30:36.7258382+05:30","level":"DEBUG","logger_name":"com.inspeedia.vanning.service.TaskService","message":"Fetching all tasks by combining planned and actual work"}
{"@timestamp":"2025-09-01T17:30:36.7395575+05:30","level":"DEBUG","logger_name":"com.inspeedia.vanning.service.TaskService","message":"Fetching all tasks by combining planned and actual work"}
{"@timestamp":"2025-09-01T17:30:36.7574179+05:30","level":"DEBUG","logger_name":"com.inspeedia.vanning.service.TaskService","message":"Fetching today's tasks"}
]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>

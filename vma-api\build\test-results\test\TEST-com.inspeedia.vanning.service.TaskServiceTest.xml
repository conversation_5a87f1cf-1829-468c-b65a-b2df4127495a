<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.inspeedia.vanning.service.TaskServiceTest" tests="4" skipped="0" failures="0" errors="0" timestamp="2025-09-01T09:30:01" hostname="MSI" time="6.044">
  <properties/>
  <testcase name="testGetAllTasksWithoutActualWork()" classname="com.inspeedia.vanning.service.TaskServiceTest" time="5.899"/>
  <testcase name="testGetAllTasks()" classname="com.inspeedia.vanning.service.TaskServiceTest" time="0.027"/>
  <testcase name="testGetAllTasksEmpty()" classname="com.inspeedia.vanning.service.TaskServiceTest" time="0.038"/>
  <testcase name="testGetTodaysTasks()" classname="com.inspeedia.vanning.service.TaskServiceTest" time="0.055"/>
  <system-out><![CDATA[]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>

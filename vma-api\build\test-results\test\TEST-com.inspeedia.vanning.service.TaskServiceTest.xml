<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.inspeedia.vanning.service.TaskServiceTest" tests="4" skipped="0" failures="0" errors="0" timestamp="2025-09-01T12:10:47" hostname="MSI" time="0.178">
  <properties/>
  <testcase name="testGetAllTasksWithoutActualWork()" classname="com.inspeedia.vanning.service.TaskServiceTest" time="0.16"/>
  <testcase name="testGetAllTasks()" classname="com.inspeedia.vanning.service.TaskServiceTest" time="0.004"/>
  <testcase name="testGetAllTasksEmpty()" classname="com.inspeedia.vanning.service.TaskServiceTest" time="0.002"/>
  <testcase name="testGetTodaysTasks()" classname="com.inspeedia.vanning.service.TaskServiceTest" time="0.009"/>
  <system-out><![CDATA[]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>

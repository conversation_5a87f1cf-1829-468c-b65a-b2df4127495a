<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.inspeedia.vanning.domain.ActualWorkTest" tests="8" skipped="0" failures="0" errors="0" timestamp="2025-09-02T05:14:32" hostname="MSI" time="0.079">
  <properties/>
  <testcase name="testGettersAndSetters()" classname="com.inspeedia.vanning.domain.ActualWorkTest" time="0.001"/>
  <testcase name="testParameterizedConstructor()" classname="com.inspeedia.vanning.domain.ActualWorkTest" time="0.002"/>
  <testcase name="testToString()" classname="com.inspeedia.vanning.domain.ActualWorkTest" time="0.058"/>
  <testcase name="testFullConstructor()" classname="com.inspeedia.vanning.domain.ActualWorkTest" time="0.008"/>
  <testcase name="testInheritanceFromBaseEntity()" classname="com.inspeedia.vanning.domain.ActualWorkTest" time="0.001"/>
  <testcase name="testHashCode()" classname="com.inspeedia.vanning.domain.ActualWorkTest" time="0.0"/>
  <testcase name="testEquals()" classname="com.inspeedia.vanning.domain.ActualWorkTest" time="0.001"/>
  <testcase name="testDefaultConstructor()" classname="com.inspeedia.vanning.domain.ActualWorkTest" time="0.001"/>
  <system-out><![CDATA[]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>

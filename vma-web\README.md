# VMApp - React + TypeScript + Vite

VMApp is a React + TypeScript app scaffolded with Vite, Material UI, Tailwind CSS, i18n, MSW for mocks, and an enterprise-ready project structure designed for scale.

## Features

- Dashboard and Settings pages (Dashboard is default)
- Responsive layout with orientation-aware pagination
- Table + Bar chart per element; chart reflects selected table row
- Mock API with MSW (dev only)
- Code splitting with React.lazy and Suspense
- i18n (English/Japanese) with runtime language switcher
- Light/Dark theme toggle (Zustand-backed)
- E<PERSON>int, Prettier, EditorConfig for code quality
- Comprehensive testing with Jest and React Testing Library
- Changeset-based versioning and release management

## Project Structure (src)

```
src/
├── app/
│   ├── dashboard/              # Dashboard feature module
│   │   ├── components/
│   │   ├── services/
│   │   ├── types/
│   │   └── index.ts
│   ├── layout/                 # Header, Layout
│   └── routes/                 # Route configuration
├── common/                     # Shared components & hooks
├── core/                       # Infrastructure: api, store, providers, config, error, i18n
│   ├── api/
│   ├── store/
│   ├── providers/
│   ├── config/
│   ├── error/
│   └── i18n/
├── mocks/                      # MSW handlers & browser worker bootstrap
├── pages/                      # Page entry points
└── index.css                   # Global styles (Tailwind v4)
```

## Scripts

- `npm run dev` – start dev server
- `npm run build` – type-check and build production bundle
- `npm run preview` – preview production build
- `npm run lint` – run ESLint
- `npm run typecheck` – TypeScript project references type-check
- `npm run format` – Prettier format all files
- `npm run test` – run Jest tests
- `npm run test:watch` – run tests in watch mode
- `npm run test:coverage` – run tests with coverage report
- `npm run changeset` – create a new changeset for versioning
- `npm run version-packages` – version packages based on changesets
- `npm run release` – publish packages to registry

## Tooling

- Material UI v7, Tailwind v4, React Router v7
- Recharts for charts
- MSW for browser-side mocking (service worker in `public/`)
- i18next + react-i18next
- Zustand for app store
- Jest + React Testing Library for testing
- Prettier + EditorConfig for consistent formatting (Windows/macOS friendly)
- ESLint for code quality and best practices
- Changesets for versioning and release management

## Development Features

- Hot module replacement with Vite
- TypeScript strict mode enabled
- ESLint with React Hooks and TypeScript rules
- Prettier with custom formatting rules (tabs, single quotes)
- Jest configuration with coverage reporting
- MSW for API mocking in development
- Proxy configuration for backend API calls

## Setup

See [SETUP.md](SETUP.md) for cross-OS installation and run instructions.

## Contributing

See [CONTRIBUTING.md](CONTRIBUTING.md) for code style, branching, and PR guidelines.

<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.inspeedia.vanning.exception.ErrorResponseTest" tests="13" skipped="0" failures="0" errors="0" timestamp="2025-09-02T05:14:33" hostname="MSI" time="0.06">
  <properties/>
  <testcase name="testGettersAndSetters()" classname="com.inspeedia.vanning.exception.ErrorResponseTest" time="0.002"/>
  <testcase name="testParameterizedConstructor()" classname="com.inspeedia.vanning.exception.ErrorResponseTest" time="0.002"/>
  <testcase name="testToString()" classname="com.inspeedia.vanning.exception.ErrorResponseTest" time="0.025"/>
  <testcase name="testValidationErrorBuilder()" classname="com.inspeedia.vanning.exception.ErrorResponseTest" time="0.006"/>
  <testcase name="testValidationError()" classname="com.inspeedia.vanning.exception.ErrorResponseTest" time="0.001"/>
  <testcase name="testValidationErrorToString()" classname="com.inspeedia.vanning.exception.ErrorResponseTest" time="0.003"/>
  <testcase name="testBuilder()" classname="com.inspeedia.vanning.exception.ErrorResponseTest" time="0.008"/>
  <testcase name="testValidationErrorConstructor()" classname="com.inspeedia.vanning.exception.ErrorResponseTest" time="0.001"/>
  <testcase name="testValidationErrorEquals()" classname="com.inspeedia.vanning.exception.ErrorResponseTest" time="0.001"/>
  <testcase name="testHashCode()" classname="com.inspeedia.vanning.exception.ErrorResponseTest" time="0.001"/>
  <testcase name="testEquals()" classname="com.inspeedia.vanning.exception.ErrorResponseTest" time="0.001"/>
  <testcase name="testValidationErrorHashCode()" classname="com.inspeedia.vanning.exception.ErrorResponseTest" time="0.001"/>
  <testcase name="testDefaultConstructor()" classname="com.inspeedia.vanning.exception.ErrorResponseTest" time="0.001"/>
  <system-out><![CDATA[]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>

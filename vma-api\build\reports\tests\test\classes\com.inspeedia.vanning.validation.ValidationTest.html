<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<meta http-equiv="x-ua-compatible" content="IE=edge"/>
<title>Test results - ValidationTest</title>
<link href="../css/base-style.css" rel="stylesheet" type="text/css"/>
<link href="../css/style.css" rel="stylesheet" type="text/css"/>
<script src="../js/report.js" type="text/javascript"></script>
</head>
<body>
<div id="content">
<h1>ValidationTest</h1>
<div class="breadcrumbs">
<a href="../index.html">all</a> &gt; 
<a href="../packages/com.inspeedia.vanning.validation.html">com.inspeedia.vanning.validation</a> &gt; ValidationTest</div>
<div id="summary">
<table>
<tr>
<td>
<div class="summaryGroup">
<table>
<tr>
<td>
<div class="infoBox" id="tests">
<div class="counter">11</div>
<p>tests</p>
</div>
</td>
<td>
<div class="infoBox" id="failures">
<div class="counter">0</div>
<p>failures</p>
</div>
</td>
<td>
<div class="infoBox" id="ignored">
<div class="counter">0</div>
<p>ignored</p>
</div>
</td>
<td>
<div class="infoBox" id="duration">
<div class="counter">0.486s</div>
<p>duration</p>
</div>
</td>
</tr>
</table>
</div>
</td>
<td>
<div class="infoBox success" id="successRate">
<div class="percent">100%</div>
<p>successful</p>
</div>
</td>
</tr>
</table>
</div>
<div id="tabs">
<ul class="tabLinks">
<li>
<a href="#tab0">Tests</a>
</li>
</ul>
<div id="tab0" class="tab">
<h2>Tests</h2>
<table>
<thead>
<tr>
<th>Test</th>
<th>Duration</th>
<th>Result</th>
</tr>
</thead>
<tr>
<td class="success">testActualWorkUserNameTooLong()</td>
<td class="success">0.030s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testInvalidActualWorkBlankUserName()</td>
<td class="success">0.019s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testInvalidActualWorkProgressOutOfRange()</td>
<td class="success">0.188s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testInvalidActualWorkProgressRateOutOfRange()</td>
<td class="success">0.022s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testInvalidPlannedWorkPlatformWrongLength()</td>
<td class="success">0.043s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testInvalidPlannedWorkSizeWrongLength()</td>
<td class="success">0.017s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testInvalidPlannedWorkVanGpWrongLength()</td>
<td class="success">0.059s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testInvalidPlannedWorkVanGpWrongPattern()</td>
<td class="success">0.042s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testMultipleValidationErrors()</td>
<td class="success">0.017s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testValidActualWork()</td>
<td class="success">0.024s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testValidPlannedWork()</td>
<td class="success">0.025s</td>
<td class="success">passed</td>
</tr>
</table>
</div>
</div>
<div id="footer">
<p>
<div>
<label class="hidden" id="label-for-line-wrapping-toggle" for="line-wrapping-toggle">Wrap lines
<input id="line-wrapping-toggle" type="checkbox" autocomplete="off"/>
</label>
</div>Generated by 
<a href="http://www.gradle.org">Gradle 8.5</a> at Sep 1, 2025, 5:40:48 PM</p>
</div>
</div>
</body>
</html>

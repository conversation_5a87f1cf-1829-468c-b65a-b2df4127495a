/**
 * Jest setup file for global test configuration
 */

import '@testing-library/jest-dom'

// Mock window.matchMedia for orientation tests
Object.defineProperty(window, 'matchMedia', {
	writable: true,
	value: jest.fn().mockImplementation((query) => ({
		matches: false,
		media: query,
		onchange: null,
		addListener: jest.fn(), // Deprecated
		removeListener: jest.fn(), // Deprecated
		addEventListener: jest.fn(),
		removeEventListener: jest.fn(),
		dispatchEvent: jest.fn(),
	})),
})

// Mock ResizeObserver
;(globalThis as any).ResizeObserver = jest.fn().mockImplementation(() => ({
	observe: jest.fn(),
	unobserve: jest.fn(),
	disconnect: jest.fn(),
}))

// Mock IntersectionObserver
;(globalThis as any).IntersectionObserver = jest.fn().mockImplementation(() => ({
	observe: jest.fn(),
	unobserve: jest.fn(),
	disconnect: jest.fn(),
}))

// Mock scrollTo
Object.defineProperty(window, 'scrollTo', {
	value: jest.fn(),
	writable: true,
})

// Mock console methods to reduce noise in tests
const originalConsoleError = console.error
const originalConsoleWarn = console.warn

beforeAll(() => {
	// eslint-disable-next-line @typescript-eslint/no-explicit-any
	console.error = (...args: any[]) => {
		if (
			typeof args[0] === 'string' &&
			(args[0].includes('Warning: ReactDOM.render is no longer supported') ||
				args[0].includes('Warning: useLayoutEffect does nothing on the server'))
		) {
			return
		}
		originalConsoleError.call(console, ...args)
	}

	// eslint-disable-next-line @typescript-eslint/no-explicit-any
	console.warn = (...args: any[]) => {
		if (
			typeof args[0] === 'string' &&
			args[0].includes('Warning: componentWillReceiveProps has been renamed')
		) {
			return
		}
		originalConsoleWarn.call(console, ...args)
	}
})

afterAll(() => {
	console.error = originalConsoleError
	console.warn = originalConsoleWarn
})

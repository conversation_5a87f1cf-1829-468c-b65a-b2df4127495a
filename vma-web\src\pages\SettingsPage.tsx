import {
	Paper,
	Typography,
	Button,
	FormControl,
	InputLabel,
	MenuItem,
	Select,
	Slider,
	Box,
	Divider,
	Alert,
	Snackbar,
} from '@mui/material'
import { useRef, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { useAppStore } from '../core/store'
import { updateLocale } from '../core/i18n'
import { resetSettings } from '../core/utils/settings'
import RestartAltIcon from '@mui/icons-material/RestartAlt'

export default function SettingsPage() {
	const { t } = useTranslation()
	const inputRef = useRef<HTMLInputElement | null>(null)
	const [fileName, setFileName] = useState<string>('')
	const [notification, setNotification] = useState<{ message: string; severity: 'success' | 'error' | 'info' } | null>(null)

	const locale = useAppStore((s) => s.locale)
	const setLocale = useAppStore((s) => s.setLocale)
	const fontScale = useAppStore((s) => s.fontScale)
	const setFontScale = useAppStore((s) => s.setFontScale)
	const setThemeMode = useAppStore((s) => s.setThemeMode)

	const showNotification = (message: string, severity: 'success' | 'error' | 'info' = 'success') => {
		setNotification({ message, severity })
	}

	const closeNotification = () => {
		setNotification(null)
	}

	async function handleLocaleChange(value: 'en' | 'ja') {
		setLocale(value)
		try {
			await updateLocale(value)
			showNotification(t('settings.languageChanged', { defaultValue: 'Language changed successfully' }))
		} catch (error) {
			console.error('Failed to update locale:', error)
			showNotification(t('settings.languageError', { defaultValue: 'Failed to change language' }), 'error')
		}
	}

	const handleFontScaleChange = (value: number) => {
		setFontScale(value)
		showNotification(t('settings.fontScaleChanged', { defaultValue: 'Font scale updated' }), 'info')
	}



	const handleResetSettings = async () => {
		try {
			const defaultSettings = resetSettings()
			setLocale(defaultSettings.locale)
			setThemeMode(defaultSettings.themeMode)
			setFontScale(defaultSettings.fontScale)

			// Update i18n if locale changed
			if (defaultSettings.locale !== locale) {
				await updateLocale(defaultSettings.locale)
			}

			showNotification(t('settings.resetSuccess', { defaultValue: 'Settings reset to defaults' }))
		} catch (error) {
			console.error('Reset failed:', error)
			showNotification(t('settings.resetError', { defaultValue: 'Failed to reset settings' }), 'error')
		}
	}

	return (
		<>
			<Paper className="p-4 space-y-6">
				<Typography variant="h5">{t('nav.settings')}</Typography>

				<Box className="space-y-2 p-3">
					<FormControl size="small" sx={{ minWidth: 200 }}>
						<InputLabel id="locale-label">Language</InputLabel>
						<Select
							labelId="locale-label"
							label={t('settings.language', { defaultValue: 'Language' })}
							value={locale}
							onChange={(e) => handleLocaleChange(e.target.value as 'en' | 'ja')}
						>
							<MenuItem value="en">English</MenuItem>
							<MenuItem value="ja">日本語</MenuItem>
						</Select>
					</FormControl>
				</Box>

				<Divider />

				<Box className="space-y-2 p-3">
					<Typography variant="subtitle1" fontWeight={600}>
						{t('settings.accessibility', { defaultValue: 'Accessibility' })}
					</Typography>
					<Box className="flex flex-col sm:flex-row sm:items-center gap-3">
						<Typography variant="body2" sx={{ minWidth: 140 }}>
							{t('settings.fontScale', { defaultValue: 'Font scale' })}
						</Typography>
						<Slider
							value={fontScale}
							min={0.85}
							max={1.3}
							step={0.05}
							valueLabelDisplay="auto"
							marks
							onChange={(_, v) => handleFontScaleChange(Array.isArray(v) ? v[0] : v)}
							sx={{ maxWidth: 320 }}
						/>
						<Typography variant="body2">{(fontScale * 100).toFixed(0)}%</Typography>
					</Box>
				</Box>

				<Divider />

				<Box className="space-y-2 p-3">
					<Typography variant="subtitle1" fontWeight={600}>
						{t('settings.data', { defaultValue: 'Data' })}
					</Typography>
					<div className="flex items-center gap-3">
						<input
							ref={inputRef}
							type="file"
							accept=".xlsx,.xls,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel"
							className="hidden"
							onChange={(e) => setFileName(e.target.files?.[0]?.name ?? '')}
						/>
						<Button variant="contained" onClick={() => inputRef.current?.click()}>
							{t('settings.import', { defaultValue: 'Import Excel' })}
						</Button>
						<Button
							variant="outlined"
							startIcon={<RestartAltIcon />}
							onClick={handleResetSettings}
							color="warning"
						>
							{t('settings.reset', { defaultValue: 'Reset Settings to Defaults' })}
						</Button>
						{fileName && (
							<Typography variant="body2">
								{t('settings.selected', { defaultValue: 'Selected' })}: {fileName}
							</Typography>
						)}
					</div>
				</Box>
			</Paper>

			{/* Notification Snackbar */}
			<Snackbar
				open={!!notification}
				autoHideDuration={4000}
				onClose={closeNotification}
				anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
			>
				<Alert
					onClose={closeNotification}
					severity={notification?.severity || 'success'}
					sx={{ width: '100%' }}
				>
					{notification?.message}
				</Alert>
			</Snackbar>
		</>
	)
}

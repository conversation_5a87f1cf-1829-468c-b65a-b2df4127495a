import { Suspense, lazy, useEffect, useState, forwardRef, memo } from 'react'
import { Paper, type PaperProps } from '@mui/material'
import DataTable from './DataTable'
import type { Task } from '../../../models/Task'

const LazyChartCard = lazy(() => import('./ChartCard'))

interface ElementCardProps extends PaperProps {
	tasks: Task[]
}

const ElementCardComponent = forwardRef<HTMLDivElement, ElementCardProps>(function ElementCard(
	{ tasks, className, ...paperProps }: ElementCardProps,
	ref,
) {
	const initial = tasks[0] ?? null
	const [selected, setSelected] = useState<Task | null>(initial)

	useEffect(() => {
		if (!selected && tasks.length > 0) {
			setSelected(tasks[0])
		} else if (selected && tasks.length > 0) {
			const latest = tasks.find((t) => t.id === selected.id)
			if (latest && latest !== selected) {
				setSelected(latest)
			} else if (!latest) {
				// Selected task is no longer in the array, select the first available task
				setSelected(tasks[0])
			}
		} else if (selected && tasks.length === 0) {
			// No tasks available, clear selection
			setSelected(null)
		}
	}, [tasks, selected])

	return (
		<Paper
			ref={ref}
			variant="outlined"
			className={`space-y-4 w-full p-3 sm:p-4${className ? ` ${className}` : ''}`}
			{...paperProps}
		>
			<div className="grid grid-cols-1 gap-3 sm:gap-4 md:grid-cols-3">
				<div className="min-h-64 md:min-h-72 md:col-span-2">
					<DataTable
						rows={tasks}
						selectedRowId={selected?.id ?? null}
						onSelect={(row) => setSelected(row)}
					/>
				</div>
				<div className="min-h-64 md:min-h-72 md:col-span-1">
					<Suspense fallback={<div className="p-4">Loading chart…</div>}>
						<LazyChartCard selectedTask={selected} />
					</Suspense>
				</div>
			</div>
		</Paper>
	)
})

function equalTasks(a: Task[], b: Task[]): boolean {
	if (a === b) return true
	if (a.length !== b.length) return false
	for (let i = 0; i < a.length; i++) {
		const x = a[i]
		const y = b[i]
		if (
			x.id !== y.id ||
			x.name !== y.name ||
			x.progress !== y.progress ||
			x.plannedStart !== y.plannedStart ||
			x.plannedEnd !== y.plannedEnd ||
			x.plannedDuration !== y.plannedDuration ||
			x.actualStart !== y.actualStart ||
			x.actualEnd !== y.actualEnd ||
			x.actualDuration !== y.actualDuration
		) {
			return false
		}
	}
	return true
}

const ElementCard = memo(ElementCardComponent, (prev, next) => {
	return equalTasks(prev.tasks, next.tasks) && prev.className === next.className
})

export default ElementCard

<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.inspeedia.vanning.config.AppPropertiesTest" tests="14" skipped="0" failures="0" errors="0" timestamp="2025-09-01T12:10:46" hostname="MSI" time="0.178">
  <properties/>
  <testcase name="testCorsConfiguration()" classname="com.inspeedia.vanning.config.AppPropertiesTest" time="0.101"/>
  <testcase name="testPaginationEquals()" classname="com.inspeedia.vanning.config.AppPropertiesTest" time="0.015"/>
  <testcase name="testParameterizedConstructor()" classname="com.inspeedia.vanning.config.AppPropertiesTest" time="0.001"/>
  <testcase name="testToString()" classname="com.inspeedia.vanning.config.AppPropertiesTest" time="0.033"/>
  <testcase name="testApiConstructor()" classname="com.inspeedia.vanning.config.AppPropertiesTest" time="0.001"/>
  <testcase name="testApiConfiguration()" classname="com.inspeedia.vanning.config.AppPropertiesTest" time="0.002"/>
  <testcase name="testPaginationConstructor()" classname="com.inspeedia.vanning.config.AppPropertiesTest" time="0.001"/>
  <testcase name="testApiEquals()" classname="com.inspeedia.vanning.config.AppPropertiesTest" time="0.001"/>
  <testcase name="testPaginationConfiguration()" classname="com.inspeedia.vanning.config.AppPropertiesTest" time="0.001"/>
  <testcase name="testCorsEquals()" classname="com.inspeedia.vanning.config.AppPropertiesTest" time="0.001"/>
  <testcase name="testNestedClassesIndependence()" classname="com.inspeedia.vanning.config.AppPropertiesTest" time="0.004"/>
  <testcase name="testHashCode()" classname="com.inspeedia.vanning.config.AppPropertiesTest" time="0.001"/>
  <testcase name="testCorsConstructor()" classname="com.inspeedia.vanning.config.AppPropertiesTest" time="0.001"/>
  <testcase name="testDefaultConstructor()" classname="com.inspeedia.vanning.config.AppPropertiesTest" time="0.001"/>
  <system-out><![CDATA[]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>

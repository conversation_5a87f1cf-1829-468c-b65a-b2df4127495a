package com.inspeedia.vanning.controller;

import com.inspeedia.vanning.dto.TaskDto;
import com.inspeedia.vanning.service.TaskService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.reactive.WebFluxTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.web.reactive.server.WebTestClient;
import reactor.core.publisher.Flux;

import static org.mockito.Mockito.when;

/**
 * Unit tests for TaskController
 */
@WebFluxTest(TaskController.class)
class TaskControllerTest {

    @Autowired
    private WebTestClient webTestClient;

    @MockBean
    private TaskService taskService;

    private TaskDto sampleTask;

    @BeforeEach
    void setUp() {
        sampleTask = new TaskDto();
        sampleTask.setId(1L);
        sampleTask.setName("AB - C to D");
        sampleTask.setNo("T001");
        sampleTask.setShippingDate("2024-01-15");
        sampleTask.setVanGp("AB");
        sampleTask.setDeliveryTime("07:30");
        sampleTask.setPlannedStart("08:00");
        sampleTask.setPlannedEnd("17:00");
        sampleTask.setPlannedDuration("9h");
        sampleTask.setActualStart("08:15");
        sampleTask.setActualEnd("17:30");
        sampleTask.setActualDuration("9h 15m");
        sampleTask.setProgress(85);
    }

    @Test
    void testGetAllTasks() {
        when(taskService.getAllTasks()).thenReturn(Flux.just(sampleTask));

        webTestClient.get()
                .uri("/api/v1/tasks")
                .exchange()
                .expectStatus().isOk()
                .expectBodyList(TaskDto.class)
                .hasSize(1)
                .contains(sampleTask);
    }

    @Test
    void testGetTodaysTasks() {
        when(taskService.getTodaysTasks()).thenReturn(Flux.just(sampleTask));

        webTestClient.get()
                .uri("/api/v1/tasks/today")
                .exchange()
                .expectStatus().isOk()
                .expectBodyList(TaskDto.class)
                .hasSize(1)
                .contains(sampleTask);
    }

    @Test
    void testGetAllTasksEmpty() {
        when(taskService.getAllTasks()).thenReturn(Flux.empty());

        webTestClient.get()
                .uri("/api/v1/tasks")
                .exchange()
                .expectStatus().isOk()
                .expectBodyList(TaskDto.class)
                .hasSize(0);
    }

    @Test
    void testGetTodaysTasksEmpty() {
        when(taskService.getTodaysTasks()).thenReturn(Flux.empty());

        webTestClient.get()
                .uri("/api/v1/tasks/today")
                .exchange()
                .expectStatus().isOk()
                .expectBodyList(TaskDto.class)
                .hasSize(0);
    }
}

package com.inspeedia.vanning.controller;

import com.inspeedia.vanning.dto.TaskDto;
import com.inspeedia.vanning.service.TaskService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Flux;
import reactor.test.StepVerifier;

import static org.mockito.Mockito.when;

/**
 * Unit tests for TaskController
 */
@ExtendWith(MockitoExtension.class)
class TaskControllerTest {

    @Mock
    private TaskService taskService;

    @InjectMocks
    private TaskController taskController;

    private TaskDto sampleTask;

    @BeforeEach
    void setUp() {
        sampleTask = new TaskDto();
        sampleTask.setId(1L);
        sampleTask.setName("AB - C to D");
        sampleTask.setNo("T001");
        sampleTask.setShippingDate("2024-01-15");
        sampleTask.setVanGp("AB");
        sampleTask.setDeliveryTime("07:30");
        sampleTask.setPlannedStart("08:00");
        sampleTask.setPlannedEnd("17:00");
        sampleTask.setPlannedDuration("9h");
        sampleTask.setActualStart("08:15");
        sampleTask.setActualEnd("17:30");
        sampleTask.setActualDuration("9h 15m");
        sampleTask.setProgress(85);
    }

    @Test
    void testGetAllTasks() {
        when(taskService.getAllTasks()).thenReturn(Flux.just(sampleTask));

        Flux<TaskDto> result = taskController.getAllTasks();

        StepVerifier.create(result)
                .expectNext(sampleTask)
                .verifyComplete();
    }

    @Test
    void testGetTodaysTasks() {
        when(taskService.getTodaysTasks()).thenReturn(Flux.just(sampleTask));

        Flux<TaskDto> result = taskController.getTodaysTasks();

        StepVerifier.create(result)
                .expectNext(sampleTask)
                .verifyComplete();
    }

    @Test
    void testGetAllTasksEmpty() {
        when(taskService.getAllTasks()).thenReturn(Flux.empty());

        Flux<TaskDto> result = taskController.getAllTasks();

        StepVerifier.create(result)
                .verifyComplete();
    }

    @Test
    void testGetTodaysTasksEmpty() {
        when(taskService.getTodaysTasks()).thenReturn(Flux.empty());

        Flux<TaskDto> result = taskController.getTodaysTasks();

        StepVerifier.create(result)
                .verifyComplete();
    }
}

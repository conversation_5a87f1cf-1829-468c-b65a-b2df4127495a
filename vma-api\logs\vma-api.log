{"@timestamp":"2025-09-01T16:35:29.4506051+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.client.ReactorNettyClient","message":"[cid: 0x5] Response: TABNAME [names=[[actual_work]]]"}
{"@timestamp":"2025-09-01T16:35:29.4506051+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.client.ReactorNettyClient","message":"[cid: 0x5] Response: ColInfoToken [columns=[ColInfo [column=1, table=1, status=8, columnName=\"null\"], ColInfo [column=2, table=1, status=0, columnName=\"null\"], ColInfo [column=3, table=1, status=0, columnName=\"null\"], ColInfo [column=4, table=1, status=0, columnName=\"null\"], ColInfo [column=5, table=1, status=0, columnName=\"null\"], ColInfo [column=6, table=1, status=0, columnName=\"null\"], ColInfo [column=7, table=1, status=0, columnName=\"null\"], ColInfo [column=8, table=1, status=0, columnName=\"null\"], ColInfo [column=9, table=1, status=0, columnName=\"null\"], ColInfo [column=10, table=1, status=0, columnName=\"null\"], ColInfo [column=11, table=1, status=0, columnName=\"null\"], ColInfo [column=12, table=1, status=0, columnName=\"null\"], ColInfo [column=13, table=1, status=0, columnName=\"null\"], ColInfo [column=14, table=0, status=20, columnName=\"null\"]]]"}
{"@timestamp":"2025-09-01T16:35:29.4506051+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.client.ReactorNettyClient","message":"[cid: 0x5] Response: OrderToken [orderByColumns=[2]]"}
{"@timestamp":"2025-09-01T16:35:29.4506051+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.client.ReactorNettyClient","message":"[cid: 0x5] Response: DoneInProcToken [done=false, hasCount=true, rowCount=0, hasMore=true, attnAck=false, currentCommand=0]"}
{"@timestamp":"2025-09-01T16:35:29.4506051+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.client.ReactorNettyClient","message":"[cid: 0x5] Response: ReturnStatus [status=0]"}
{"@timestamp":"2025-09-01T16:35:29.4506051+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.client.ReactorNettyClient","message":"[cid: 0x5] Response: ReturnValue [ordinal=1, parameterName='', value=PooledSlicedByteBuf(ridx: 0, widx: 5, cap: 5/5, unwrapped: PooledUnsafeDirectByteBuf(ridx: 528, widx: 528, cap: 1024)), type=MutableTypeInformation [maxLength=4, lengthStrategy=BYTELENTYPE, precision=10, displaySize=11, scale=0, flags=0, serverType=int, userType=0, udtTypeName=\"null\", collation=null, charset=null]]"}
{"@timestamp":"2025-09-01T16:35:29.4506051+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.RpcQueryMessageFlow","message":"CursorId: 180150353"}
{"@timestamp":"2025-09-01T16:35:29.4506051+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.client.ReactorNettyClient","message":"[cid: 0x5] Response: ReturnValue [ordinal=4, parameterName='', value=PooledSlicedByteBuf(ridx: 0, widx: 5, cap: 5/5, unwrapped: PooledUnsafeDirectByteBuf(ridx: 528, widx: 528, cap: 1024)), type=MutableTypeInformation [maxLength=4, lengthStrategy=BYTELENTYPE, precision=10, displaySize=11, scale=0, flags=0, serverType=int, userType=0, udtTypeName=\"null\", collation=null, charset=null]]"}
{"@timestamp":"2025-09-01T16:35:29.4506051+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.client.ReactorNettyClient","message":"[cid: 0x5] Response: DoneProcToken [done=true, hasCount=false, rowCount=0, hasMore=false, attnAck=false, currentCommand=0]"}
{"@timestamp":"2025-09-01T16:35:29.4506051+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.client.ReactorNettyClient","message":"[cid: 0x5] Request: RPCRequest [procName='null', procId=7, optionFlags=io.r2dbc.mssql.message.token.RpcRequest$OptionFlags@3073bef7, statusFlags=0, parameterDescriptors=[RpcInt [name='null', value=180150353], RpcInt [name='null', value=2], RpcInt [name='null', value=0], RpcInt [name='null', value=128]]]"}
{"@timestamp":"2025-09-01T16:35:29.4531238+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.client.ReactorNettyClient","message":"[cid: 0x5] Response: ColumnMetadataToken [columns=[]]"}
{"@timestamp":"2025-09-01T16:35:29.4531238+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.client.ReactorNettyClient","message":"[cid: 0x5] Response: io.r2dbc.mssql.message.token.RowToken@53fb951c"}
{"@timestamp":"2025-09-01T16:35:29.4537258+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.client.ReactorNettyClient","message":"[cid: 0x5] Response: io.r2dbc.mssql.message.token.RowToken@7daa4bcb"}
{"@timestamp":"2025-09-01T16:35:29.4537258+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.client.ReactorNettyClient","message":"[cid: 0x5] Response: io.r2dbc.mssql.message.token.RowToken@61751cd9"}
{"@timestamp":"2025-09-01T16:35:29.4543466+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.client.ReactorNettyClient","message":"[cid: 0x5] Response: io.r2dbc.mssql.message.token.RowToken@470149aa"}
{"@timestamp":"2025-09-01T16:35:29.4543466+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.client.ReactorNettyClient","message":"[cid: 0x5] Response: io.r2dbc.mssql.message.token.RowToken@485d6723"}
{"@timestamp":"2025-09-01T16:35:29.4543466+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.client.ReactorNettyClient","message":"[cid: 0x5] Response: io.r2dbc.mssql.message.token.RowToken@678689f0"}
{"@timestamp":"2025-09-01T16:35:29.4543466+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.client.ReactorNettyClient","message":"[cid: 0x5] Response: io.r2dbc.mssql.message.token.RowToken@2edcc20d"}
{"@timestamp":"2025-09-01T16:35:29.4543466+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.client.ReactorNettyClient","message":"[cid: 0x5] Response: io.r2dbc.mssql.message.token.RowToken@63dd8265"}
{"@timestamp":"2025-09-01T16:35:29.4543466+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.client.ReactorNettyClient","message":"[cid: 0x5] Response: io.r2dbc.mssql.message.token.RowToken@401a8542"}
{"@timestamp":"2025-09-01T16:35:29.4543466+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.client.ReactorNettyClient","message":"[cid: 0x5] Response: io.r2dbc.mssql.message.token.RowToken@13804db8"}
{"@timestamp":"2025-09-01T16:35:29.4543466+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.client.ReactorNettyClient","message":"[cid: 0x5] Response: io.r2dbc.mssql.message.token.RowToken@5b04296f"}
{"@timestamp":"2025-09-01T16:35:29.4543466+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.client.ReactorNettyClient","message":"[cid: 0x5] Response: io.r2dbc.mssql.message.token.RowToken@54c546c0"}
{"@timestamp":"2025-09-01T16:35:29.4543466+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.client.ReactorNettyClient","message":"[cid: 0x5] Response: io.r2dbc.mssql.message.token.RowToken@4061e98f"}
{"@timestamp":"2025-09-01T16:35:29.4548943+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.client.ReactorNettyClient","message":"[cid: 0x5] Response: io.r2dbc.mssql.message.token.RowToken@519d91b6"}
{"@timestamp":"2025-09-01T16:35:29.4548943+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.client.ReactorNettyClient","message":"[cid: 0x5] Response: io.r2dbc.mssql.message.token.RowToken@2ba895bb"}
{"@timestamp":"2025-09-01T16:35:29.4548943+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.client.ReactorNettyClient","message":"[cid: 0x5] Response: io.r2dbc.mssql.message.token.RowToken@55253548"}
{"@timestamp":"2025-09-01T16:35:29.4548943+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.client.ReactorNettyClient","message":"[cid: 0x5] Response: io.r2dbc.mssql.message.token.RowToken@1ac793f2"}
{"@timestamp":"2025-09-01T16:35:29.4548943+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.client.ReactorNettyClient","message":"[cid: 0x5] Response: io.r2dbc.mssql.message.token.RowToken@27eaa13d"}
{"@timestamp":"2025-09-01T16:35:29.4548943+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.client.ReactorNettyClient","message":"[cid: 0x5] Response: io.r2dbc.mssql.message.token.RowToken@4800ed36"}
{"@timestamp":"2025-09-01T16:35:29.4548943+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.client.ReactorNettyClient","message":"[cid: 0x5] Response: io.r2dbc.mssql.message.token.RowToken@34730e0b"}
{"@timestamp":"2025-09-01T16:35:29.4548943+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.client.ReactorNettyClient","message":"[cid: 0x5] Response: io.r2dbc.mssql.message.token.RowToken@120d5374"}
{"@timestamp":"2025-09-01T16:35:29.4548943+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.client.ReactorNettyClient","message":"[cid: 0x5] Response: io.r2dbc.mssql.message.token.RowToken@609cfb41"}
{"@timestamp":"2025-09-01T16:35:29.4548943+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.client.ReactorNettyClient","message":"[cid: 0x5] Response: io.r2dbc.mssql.message.token.RowToken@2f3ac339"}
{"@timestamp":"2025-09-01T16:35:29.4548943+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.client.ReactorNettyClient","message":"[cid: 0x5] Response: io.r2dbc.mssql.message.token.RowToken@3958383"}
{"@timestamp":"2025-09-01T16:35:29.4548943+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.client.ReactorNettyClient","message":"[cid: 0x5] Response: io.r2dbc.mssql.message.token.RowToken@7b7be676"}
{"@timestamp":"2025-09-01T16:35:29.4548943+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.client.ReactorNettyClient","message":"[cid: 0x5] Response: io.r2dbc.mssql.message.token.RowToken@7cba5cab"}
{"@timestamp":"2025-09-01T16:35:29.4548943+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.client.ReactorNettyClient","message":"[cid: 0x5] Response: io.r2dbc.mssql.message.token.RowToken@5e06b0e"}
{"@timestamp":"2025-09-01T16:35:29.4548943+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.client.ReactorNettyClient","message":"[cid: 0x5] Response: io.r2dbc.mssql.message.token.RowToken@2ac669aa"}
{"@timestamp":"2025-09-01T16:35:29.4548943+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.client.ReactorNettyClient","message":"[cid: 0x5] Response: io.r2dbc.mssql.message.token.RowToken@e2a72d2"}
{"@timestamp":"2025-09-01T16:35:29.4548943+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.client.ReactorNettyClient","message":"[cid: 0x5] Response: io.r2dbc.mssql.message.token.RowToken@205b38b8"}
{"@timestamp":"2025-09-01T16:35:29.4548943+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.client.ReactorNettyClient","message":"[cid: 0x5] Response: io.r2dbc.mssql.message.token.RowToken@26658fa6"}
{"@timestamp":"2025-09-01T16:35:29.4558851+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.client.ReactorNettyClient","message":"[cid: 0x5] Response: io.r2dbc.mssql.message.token.RowToken@3ff739f9"}
{"@timestamp":"2025-09-01T16:35:29.4558851+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.client.ReactorNettyClient","message":"[cid: 0x5] Response: io.r2dbc.mssql.message.token.RowToken@62966037"}
{"@timestamp":"2025-09-01T16:35:29.4558851+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.client.ReactorNettyClient","message":"[cid: 0x5] Response: io.r2dbc.mssql.message.token.RowToken@1b711a25"}
{"@timestamp":"2025-09-01T16:35:29.4558851+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.client.ReactorNettyClient","message":"[cid: 0x5] Response: io.r2dbc.mssql.message.token.RowToken@1163ae52"}
{"@timestamp":"2025-09-01T16:35:29.4558851+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.client.ReactorNettyClient","message":"[cid: 0x5] Response: io.r2dbc.mssql.message.token.RowToken@17dd7576"}
{"@timestamp":"2025-09-01T16:35:29.4558851+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.client.ReactorNettyClient","message":"[cid: 0x5] Response: io.r2dbc.mssql.message.token.RowToken@461b3862"}
{"@timestamp":"2025-09-01T16:35:29.4558851+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.client.ReactorNettyClient","message":"[cid: 0x5] Response: io.r2dbc.mssql.message.token.RowToken@55a8a2b6"}
{"@timestamp":"2025-09-01T16:35:29.4558851+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.client.ReactorNettyClient","message":"[cid: 0x5] Response: io.r2dbc.mssql.message.token.RowToken@5935876f"}
{"@timestamp":"2025-09-01T16:35:29.4558851+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.client.ReactorNettyClient","message":"[cid: 0x5] Response: io.r2dbc.mssql.message.token.RowToken@25bf2ffa"}
{"@timestamp":"2025-09-01T16:35:29.4558851+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.client.ReactorNettyClient","message":"[cid: 0x5] Response: io.r2dbc.mssql.message.token.RowToken@3af8db07"}
{"@timestamp":"2025-09-01T16:35:29.4558851+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.client.ReactorNettyClient","message":"[cid: 0x5] Response: io.r2dbc.mssql.message.token.RowToken@2a5563fc"}
{"@timestamp":"2025-09-01T16:35:29.4558851+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.client.ReactorNettyClient","message":"[cid: 0x5] Response: io.r2dbc.mssql.message.token.RowToken@5f2e8726"}
{"@timestamp":"2025-09-01T16:35:29.4558851+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.client.ReactorNettyClient","message":"[cid: 0x5] Response: io.r2dbc.mssql.message.token.RowToken@332a3c6"}
{"@timestamp":"2025-09-01T16:35:29.4558851+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.client.ReactorNettyClient","message":"[cid: 0x5] Response: io.r2dbc.mssql.message.token.RowToken@7f6e6044"}
{"@timestamp":"2025-09-01T16:35:29.456863+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.client.ReactorNettyClient","message":"[cid: 0x5] Response: io.r2dbc.mssql.message.token.RowToken@7e25a6d6"}
{"@timestamp":"2025-09-01T16:35:29.456863+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.client.ReactorNettyClient","message":"[cid: 0x5] Response: io.r2dbc.mssql.message.token.RowToken@55990983"}
{"@timestamp":"2025-09-01T16:35:29.456863+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.client.ReactorNettyClient","message":"[cid: 0x5] Response: io.r2dbc.mssql.message.token.RowToken@4323ac65"}
{"@timestamp":"2025-09-01T16:35:29.456863+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.client.ReactorNettyClient","message":"[cid: 0x5] Response: io.r2dbc.mssql.message.token.RowToken@4f438805"}
{"@timestamp":"2025-09-01T16:35:29.456863+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.client.ReactorNettyClient","message":"[cid: 0x5] Response: io.r2dbc.mssql.message.token.RowToken@1c03842d"}
{"@timestamp":"2025-09-01T16:35:29.456863+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.client.ReactorNettyClient","message":"[cid: 0x5] Response: io.r2dbc.mssql.message.token.RowToken@1cb433f"}
{"@timestamp":"2025-09-01T16:35:29.456863+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.client.ReactorNettyClient","message":"[cid: 0x5] Response: io.r2dbc.mssql.message.token.RowToken@5fd587e7"}
{"@timestamp":"2025-09-01T16:35:29.456863+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.client.ReactorNettyClient","message":"[cid: 0x5] Response: io.r2dbc.mssql.message.token.RowToken@c852fb8"}
{"@timestamp":"2025-09-01T16:35:29.456863+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.client.ReactorNettyClient","message":"[cid: 0x5] Response: io.r2dbc.mssql.message.token.RowToken@ca7e0e9"}
{"@timestamp":"2025-09-01T16:35:29.456863+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.client.ReactorNettyClient","message":"[cid: 0x5] Response: io.r2dbc.mssql.message.token.RowToken@572e88f9"}
{"@timestamp":"2025-09-01T16:35:29.456863+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.client.ReactorNettyClient","message":"[cid: 0x5] Response: io.r2dbc.mssql.message.token.RowToken@2437d744"}
{"@timestamp":"2025-09-01T16:35:29.456863+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.client.ReactorNettyClient","message":"[cid: 0x5] Response: io.r2dbc.mssql.message.token.RowToken@1ead495"}
{"@timestamp":"2025-09-01T16:35:29.456863+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.client.ReactorNettyClient","message":"[cid: 0x5] Response: io.r2dbc.mssql.message.token.RowToken@3279cce9"}
{"@timestamp":"2025-09-01T16:35:29.456863+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.client.ReactorNettyClient","message":"[cid: 0x5] Response: io.r2dbc.mssql.message.token.RowToken@1133f31f"}
{"@timestamp":"2025-09-01T16:35:29.4578372+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.client.ReactorNettyClient","message":"[cid: 0x5] Response: io.r2dbc.mssql.message.token.RowToken@ba49b8a"}
{"@timestamp":"2025-09-01T16:35:29.4578372+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.client.ReactorNettyClient","message":"[cid: 0x5] Response: io.r2dbc.mssql.message.token.RowToken@5c3db817"}
{"@timestamp":"2025-09-01T16:35:29.4578372+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.client.ReactorNettyClient","message":"[cid: 0x5] Response: io.r2dbc.mssql.message.token.RowToken@205f29fd"}
{"@timestamp":"2025-09-01T16:35:29.4578372+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.client.ReactorNettyClient","message":"[cid: 0x5] Response: io.r2dbc.mssql.message.token.RowToken@2efa8de2"}
{"@timestamp":"2025-09-01T16:35:29.4578372+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.client.ReactorNettyClient","message":"[cid: 0x5] Response: io.r2dbc.mssql.message.token.RowToken@12d3b7c"}
{"@timestamp":"2025-09-01T16:35:29.4578372+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.client.ReactorNettyClient","message":"[cid: 0x5] Response: io.r2dbc.mssql.message.token.RowToken@3061d64e"}
{"@timestamp":"2025-09-01T16:35:29.4578372+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.client.ReactorNettyClient","message":"[cid: 0x5] Response: io.r2dbc.mssql.message.token.RowToken@69a8017e"}
{"@timestamp":"2025-09-01T16:35:29.4578372+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.client.ReactorNettyClient","message":"[cid: 0x5] Response: io.r2dbc.mssql.message.token.RowToken@13c71434"}
{"@timestamp":"2025-09-01T16:35:29.4578372+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.client.ReactorNettyClient","message":"[cid: 0x5] Response: io.r2dbc.mssql.message.token.RowToken@7bd93beb"}
{"@timestamp":"2025-09-01T16:35:29.4578372+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.client.ReactorNettyClient","message":"[cid: 0x5] Response: io.r2dbc.mssql.message.token.RowToken@45345178"}
{"@timestamp":"2025-09-01T16:35:29.4578372+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.client.ReactorNettyClient","message":"[cid: 0x5] Response: io.r2dbc.mssql.message.token.RowToken@6c3e1b51"}
{"@timestamp":"2025-09-01T16:35:29.4578372+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.client.ReactorNettyClient","message":"[cid: 0x5] Response: io.r2dbc.mssql.message.token.RowToken@4773c456"}
{"@timestamp":"2025-09-01T16:35:29.4578372+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.client.ReactorNettyClient","message":"[cid: 0x5] Response: io.r2dbc.mssql.message.token.RowToken@7547a930"}
{"@timestamp":"2025-09-01T16:35:29.4588165+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.client.ReactorNettyClient","message":"[cid: 0x5] Response: io.r2dbc.mssql.message.token.RowToken@129f8971"}
{"@timestamp":"2025-09-01T16:35:29.4588165+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.client.ReactorNettyClient","message":"[cid: 0x5] Response: io.r2dbc.mssql.message.token.RowToken@ee7fce0"}
{"@timestamp":"2025-09-01T16:35:29.4588165+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.client.ReactorNettyClient","message":"[cid: 0x5] Response: io.r2dbc.mssql.message.token.RowToken@576fc81e"}
{"@timestamp":"2025-09-01T16:35:29.4588165+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.client.ReactorNettyClient","message":"[cid: 0x5] Response: io.r2dbc.mssql.message.token.RowToken@1f29e5ea"}
{"@timestamp":"2025-09-01T16:35:29.4588165+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.client.ReactorNettyClient","message":"[cid: 0x5] Response: io.r2dbc.mssql.message.token.RowToken@7113337f"}
{"@timestamp":"2025-09-01T16:35:29.4588165+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.client.ReactorNettyClient","message":"[cid: 0x5] Response: io.r2dbc.mssql.message.token.RowToken@40a2d141"}
{"@timestamp":"2025-09-01T16:35:29.4588165+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.client.ReactorNettyClient","message":"[cid: 0x5] Response: io.r2dbc.mssql.message.token.RowToken@69a4ce73"}
{"@timestamp":"2025-09-01T16:35:29.4588165+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.client.ReactorNettyClient","message":"[cid: 0x5] Response: io.r2dbc.mssql.message.token.RowToken@39f27018"}
{"@timestamp":"2025-09-01T16:35:29.4588165+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.client.ReactorNettyClient","message":"[cid: 0x5] Response: io.r2dbc.mssql.message.token.RowToken@51123492"}
{"@timestamp":"2025-09-01T16:35:29.4588165+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.client.ReactorNettyClient","message":"[cid: 0x5] Response: io.r2dbc.mssql.message.token.RowToken@76ccf28"}
{"@timestamp":"2025-09-01T16:35:29.4588165+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.client.ReactorNettyClient","message":"[cid: 0x5] Response: io.r2dbc.mssql.message.token.RowToken@48f44ee0"}
{"@timestamp":"2025-09-01T16:35:29.4588165+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.client.ReactorNettyClient","message":"[cid: 0x5] Response: io.r2dbc.mssql.message.token.RowToken@622c8556"}
{"@timestamp":"2025-09-01T16:35:29.4588165+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.client.ReactorNettyClient","message":"[cid: 0x5] Response: io.r2dbc.mssql.message.token.RowToken@225a8b25"}
{"@timestamp":"2025-09-01T16:35:29.4588165+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.client.ReactorNettyClient","message":"[cid: 0x5] Response: io.r2dbc.mssql.message.token.RowToken@432e15c9"}
{"@timestamp":"2025-09-01T16:35:29.4588165+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.client.ReactorNettyClient","message":"[cid: 0x5] Response: io.r2dbc.mssql.message.token.RowToken@2947d002"}
{"@timestamp":"2025-09-01T16:35:29.4588165+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.client.ReactorNettyClient","message":"[cid: 0x5] Response: io.r2dbc.mssql.message.token.RowToken@2fc7f649"}
{"@timestamp":"2025-09-01T16:35:29.4588165+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.client.ReactorNettyClient","message":"[cid: 0x5] Response: io.r2dbc.mssql.message.token.RowToken@59325337"}
{"@timestamp":"2025-09-01T16:35:29.4588165+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.client.ReactorNettyClient","message":"[cid: 0x5] Response: io.r2dbc.mssql.message.token.RowToken@6a819585"}
{"@timestamp":"2025-09-01T16:35:29.4588165+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.client.ReactorNettyClient","message":"[cid: 0x5] Response: io.r2dbc.mssql.message.token.RowToken@7dd18f17"}
{"@timestamp":"2025-09-01T16:35:29.4598883+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.client.ReactorNettyClient","message":"[cid: 0x5] Response: io.r2dbc.mssql.message.token.RowToken@6db44b5a"}
{"@timestamp":"2025-09-01T16:35:29.4598883+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.client.ReactorNettyClient","message":"[cid: 0x5] Response: io.r2dbc.mssql.message.token.RowToken@26540630"}
{"@timestamp":"2025-09-01T16:35:29.4598883+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.client.ReactorNettyClient","message":"[cid: 0x5] Response: io.r2dbc.mssql.message.token.RowToken@409f92c2"}
{"@timestamp":"2025-09-01T16:35:29.4598883+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.client.ReactorNettyClient","message":"[cid: 0x5] Response: io.r2dbc.mssql.message.token.RowToken@31c714f1"}
{"@timestamp":"2025-09-01T16:35:29.4598883+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.client.ReactorNettyClient","message":"[cid: 0x5] Response: DoneInProcToken [done=false, hasCount=true, rowCount=95, hasMore=true, attnAck=false, currentCommand=193]"}
{"@timestamp":"2025-09-01T16:35:29.4598883+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.client.ReactorNettyClient","message":"[cid: 0x5] Response: ReturnStatus [status=0]"}
{"@timestamp":"2025-09-01T16:35:29.4598883+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.client.ReactorNettyClient","message":"[cid: 0x5] Response: DoneProcToken [done=true, hasCount=false, rowCount=0, hasMore=false, attnAck=false, currentCommand=0]"}
{"@timestamp":"2025-09-01T16:35:29.4598883+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.client.ReactorNettyClient","message":"[cid: 0x5] Request: RPCRequest [procName='null', procId=7, optionFlags=io.r2dbc.mssql.message.token.RpcRequest$OptionFlags@3073bef7, statusFlags=0, parameterDescriptors=[RpcInt [name='null', value=180150353], RpcInt [name='null', value=2], RpcInt [name='null', value=0], RpcInt [name='null', value=128]]]"}
{"@timestamp":"2025-09-01T16:35:29.4607673+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.client.ReactorNettyClient","message":"[cid: 0x5] Response: ColumnMetadataToken [columns=[]]"}
{"@timestamp":"2025-09-01T16:35:29.4607673+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.client.ReactorNettyClient","message":"[cid: 0x5] Response: DoneInProcToken [done=false, hasCount=true, rowCount=0, hasMore=true, attnAck=false, currentCommand=0]"}
{"@timestamp":"2025-09-01T16:35:29.4607673+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.client.ReactorNettyClient","message":"[cid: 0x5] Response: ReturnStatus [status=0]"}
{"@timestamp":"2025-09-01T16:35:29.4607673+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.client.ReactorNettyClient","message":"[cid: 0x5] Response: DoneProcToken [done=true, hasCount=false, rowCount=0, hasMore=false, attnAck=false, currentCommand=0]"}
{"@timestamp":"2025-09-01T16:35:29.4607673+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.client.ReactorNettyClient","message":"[cid: 0x5] Request: RPCRequest [procName='null', procId=9, optionFlags=io.r2dbc.mssql.message.token.RpcRequest$OptionFlags@28f22fde, statusFlags=0, parameterDescriptors=[RpcInt [name='null', value=180150353]]]"}
{"@timestamp":"2025-09-01T16:35:29.4617438+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.client.ReactorNettyClient","message":"[cid: 0x5] Response: ReturnStatus [status=0]"}
{"@timestamp":"2025-09-01T16:35:29.4617438+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.client.ReactorNettyClient","message":"[cid: 0x5] Response: DoneProcToken [done=true, hasCount=false, rowCount=0, hasMore=false, attnAck=false, currentCommand=0]"}
{"@timestamp":"2025-09-01T16:35:29.4617438+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.util.FluxDiscardOnCancel","message":"received cancel signal"}
{"@timestamp":"2025-09-01T16:35:29.4627315+05:30","level":"DEBUG","logger_name":"com.inspeedia.vanning.service.ActualWorkService","message":"Completed fetching active actual work"}
{"@timestamp":"2025-09-01T16:35:29.4627315+05:30","level":"DEBUG","logger_name":"org.springframework.r2dbc.connection.R2dbcTransactionManager","message":"Initiating transaction commit"}
{"@timestamp":"2025-09-01T16:35:29.4627315+05:30","level":"DEBUG","logger_name":"org.springframework.r2dbc.connection.R2dbcTransactionManager","message":"Committing R2DBC transaction on Connection [PooledConnection[io.r2dbc.mssql.MssqlConnection@43441106]]"}
{"@timestamp":"2025-09-01T16:35:29.4627315+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.MssqlConnection","message":"[cid: 0x5] Committing transaction with status [STARTED]"}
{"@timestamp":"2025-09-01T16:35:29.4627315+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.client.ReactorNettyClient","message":"[cid: 0x5] exchange()"}
{"@timestamp":"2025-09-01T16:35:29.4627315+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.QUERY","message":"[cid: 0x5] Executing query: IF @@TRANCOUNT > 0 COMMIT TRANSACTION;"}
{"@timestamp":"2025-09-01T16:35:29.4627315+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.client.ReactorNettyClient","message":"[cid: 0x5] exchange(subscribed)"}
{"@timestamp":"2025-09-01T16:35:29.4627315+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.client.ReactorNettyClient","message":"[cid: 0x5] Queueing exchange"}
{"@timestamp":"2025-09-01T16:35:29.4627315+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.client.ReactorNettyClient","message":"[cid: 0x5] Initiating queued exchange"}
{"@timestamp":"2025-09-01T16:35:29.4627315+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.client.ReactorNettyClient","message":"[cid: 0x5] Request: SQLBatch [sql=\"IF @@TRANCOUNT > 0 COMMIT TRANSACTION;\"]"}
{"@timestamp":"2025-09-01T16:35:29.4643327+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.client.ReactorNettyClient","message":"[cid: 0x5] Response: DoneToken [done=false, hasCount=false, rowCount=0, hasMore=true, attnAck=false, currentCommand=0]"}
{"@timestamp":"2025-09-01T16:35:29.4643327+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.client.ReactorNettyClient","message":"[cid: 0x5] Response: ENVCHANGE_TOKEN [length=11, changeType=CommitTx, newValue=]"}
{"@timestamp":"2025-09-01T16:35:29.4643327+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.client.ReactorNettyClient","message":"[cid: 0x5] Transaction committed"}
{"@timestamp":"2025-09-01T16:35:29.4643327+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.client.ReactorNettyClient","message":"[cid: 0x5] Response: DoneToken [done=true, hasCount=false, rowCount=0, hasMore=false, attnAck=false, currentCommand=0]"}
{"@timestamp":"2025-09-01T16:35:29.4643327+05:30","level":"DEBUG","logger_name":"org.springframework.r2dbc.connection.R2dbcTransactionManager","message":"Releasing R2DBC Connection [PooledConnection[io.r2dbc.mssql.MssqlConnection@43441106]] after transaction"}
{"@timestamp":"2025-09-01T16:35:29.4670505+05:30","level":"DEBUG","logger_name":"io.r2dbc.mssql.client.ReactorNettyClient","message":"[cid: 0x5] Conversation complete"}
{"@timestamp":"2025-09-01T16:35:29.5811085+05:30","level":"DEBUG","logger_name":"com.inspeedia.vanning.controller.TaskController","message":"Successfully fetched all tasks"}

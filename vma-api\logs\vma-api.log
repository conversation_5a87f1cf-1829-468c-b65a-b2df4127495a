{"@timestamp":"2025-09-02T10:44:20.2940497+05:30","level":"INFO","logger_name":"com.inspeedia.vanning.ApplicationStartupTest","message":"Starting ApplicationStartupTest using Java 17.0.14 with PID 17836 (started by INSP_MSS in C:\\Dev\\Mohan\\Projects\\MSS\\Toyutsu\\vanning\\vma-api)"}
{"@timestamp":"2025-09-02T10:44:20.3118252+05:30","level":"INFO","logger_name":"com.inspeedia.vanning.ApplicationStartupTest","message":"The following 1 profile is active: \"test\""}
{"@timestamp":"2025-09-02T10:44:26.8469618+05:30","level":"INFO","logger_name":"com.inspeedia.vanning.config.SecurityConfig","message":"Configured in-memory users: admin, user"}
{"@timestamp":"2025-09-02T10:44:27.9808888+05:30","level":"INFO","logger_name":"com.inspeedia.vanning.config.WebConfig","message":"CORS configured with origins: [*], methods: [*]"}
{"@timestamp":"2025-09-02T10:44:29.3042214+05:30","level":"INFO","logger_name":"com.inspeedia.vanning.ApplicationStartupTest","message":"Started ApplicationStartupTest in 10.173 seconds (process running for 12.617)"}
{"@timestamp":"2025-09-02T10:44:30.5040397+05:30","level":"INFO","logger_name":"com.inspeedia.vanning.VmApplicationTests","message":"Starting VmApplicationTests using Java 17.0.14 with PID 17836 (started by INSP_MSS in C:\\Dev\\Mohan\\Projects\\MSS\\Toyutsu\\vanning\\vma-api)"}
{"@timestamp":"2025-09-02T10:44:30.5040397+05:30","level":"INFO","logger_name":"com.inspeedia.vanning.VmApplicationTests","message":"The following 1 profile is active: \"test\""}
{"@timestamp":"2025-09-02T10:44:31.4558778+05:30","level":"INFO","logger_name":"com.inspeedia.vanning.config.SecurityConfig","message":"Configured in-memory users: admin, user"}
{"@timestamp":"2025-09-02T10:44:31.5820885+05:30","level":"INFO","logger_name":"com.inspeedia.vanning.config.WebConfig","message":"CORS configured with origins: [*], methods: [*]"}
{"@timestamp":"2025-09-02T10:44:31.8549702+05:30","level":"INFO","logger_name":"com.inspeedia.vanning.VmApplicationTests","message":"Started VmApplicationTests in 1.43 seconds (process running for 15.17)"}
{"@timestamp":"2025-09-02T10:44:32.5709754+05:30","level":"INFO","logger_name":"com.inspeedia.vanning.controller.TaskController","message":"Fetching all tasks"}
{"@timestamp":"2025-09-02T10:44:32.8600602+05:30","level":"INFO","logger_name":"com.inspeedia.vanning.controller.TaskController","message":"Fetching all tasks"}
{"@timestamp":"2025-09-02T10:44:32.880148+05:30","level":"INFO","logger_name":"com.inspeedia.vanning.controller.TaskController","message":"Fetching today's tasks"}
{"@timestamp":"2025-09-02T10:44:32.8909853+05:30","level":"INFO","logger_name":"com.inspeedia.vanning.controller.TaskController","message":"Fetching today's tasks"}
{"@timestamp":"2025-09-02T10:44:33.8961093+05:30","level":"ERROR","logger_name":"com.inspeedia.vanning.service.ActualWorkService","message":"Error updating actual work: Actual work not found with ID: 999"}
{"@timestamp":"2025-09-02T10:44:33.9501364+05:30","level":"INFO","logger_name":"com.inspeedia.vanning.service.ActualWorkService","message":"Actual work created successfully with ID: 1"}
{"@timestamp":"2025-09-02T10:44:33.9820294+05:30","level":"INFO","logger_name":"com.inspeedia.vanning.service.ActualWorkService","message":"Actual work soft deleted successfully with ID: 1"}
{"@timestamp":"2025-09-02T10:44:33.9869565+05:30","level":"INFO","logger_name":"com.inspeedia.vanning.service.ActualWorkService","message":"Actual work updated successfully with ID: 1"}
{"@timestamp":"2025-09-02T10:44:33.9928546+05:30","level":"ERROR","logger_name":"com.inspeedia.vanning.service.ActualWorkService","message":"Error deleting actual work: Actual work not found with ID: 999"}
{"@timestamp":"2025-09-02T10:44:34.1482339+05:30","level":"INFO","logger_name":"com.inspeedia.vanning.service.PlannedWorkService","message":"Planned work updated successfully with ID: 1"}
{"@timestamp":"2025-09-02T10:44:34.163131+05:30","level":"INFO","logger_name":"com.inspeedia.vanning.service.PlannedWorkService","message":"Planned work soft deleted successfully with ID: 1"}
{"@timestamp":"2025-09-02T10:44:34.1809285+05:30","level":"INFO","logger_name":"com.inspeedia.vanning.service.PlannedWorkService","message":"Planned work created successfully with ID: 1"}
{"@timestamp":"2025-09-02T10:44:34.1838693+05:30","level":"ERROR","logger_name":"com.inspeedia.vanning.service.PlannedWorkService","message":"Error deleting planned work: Planned work not found with ID: 999"}
{"@timestamp":"2025-09-02T10:44:34.1980509+05:30","level":"ERROR","logger_name":"com.inspeedia.vanning.service.PlannedWorkService","message":"Error updating planned work: Planned work not found with ID: 999"}

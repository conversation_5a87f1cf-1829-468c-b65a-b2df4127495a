<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.inspeedia.vanning.domain.PlannedWorkTest" tests="8" skipped="0" failures="0" errors="0" timestamp="2025-09-01T12:10:47" hostname="MSI" time="0.037">
  <properties/>
  <testcase name="testGettersAndSetters()" classname="com.inspeedia.vanning.domain.PlannedWorkTest" time="0.001"/>
  <testcase name="testParameterizedConstructor()" classname="com.inspeedia.vanning.domain.PlannedWorkTest" time="0.0"/>
  <testcase name="testToString()" classname="com.inspeedia.vanning.domain.PlannedWorkTest" time="0.027"/>
  <testcase name="testFullConstructor()" classname="com.inspeedia.vanning.domain.PlannedWorkTest" time="0.001"/>
  <testcase name="testInheritanceFromBaseEntity()" classname="com.inspeedia.vanning.domain.PlannedWorkTest" time="0.001"/>
  <testcase name="testHashCode()" classname="com.inspeedia.vanning.domain.PlannedWorkTest" time="0.0"/>
  <testcase name="testEquals()" classname="com.inspeedia.vanning.domain.PlannedWorkTest" time="0.0"/>
  <testcase name="testDefaultConstructor()" classname="com.inspeedia.vanning.domain.PlannedWorkTest" time="0.001"/>
  <system-out><![CDATA[]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>

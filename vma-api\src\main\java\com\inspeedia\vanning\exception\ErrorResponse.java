package com.inspeedia.vanning.exception;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * Standard error response structure
 *
 * This class represents the standard error response format returned by the API
 * for all error scenarios.
 */
public class ErrorResponse {

    private String error;
    private String message;
    private int status;
    private String path;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS")
    private LocalDateTime timestamp;

    private String traceId;
    private List<ValidationError> validationErrors;

    // Default constructor
    public ErrorResponse() {
    }

    // Constructor with all fields
    public ErrorResponse(String error, String message, int status, String path,
            LocalDateTime timestamp, String traceId, List<ValidationError> validationErrors) {
        this.error = error;
        this.message = message;
        this.status = status;
        this.path = path;
        this.timestamp = timestamp;
        this.traceId = traceId;
        this.validationErrors = validationErrors;
    }

    // Builder pattern implementation
    public static ErrorResponseBuilder builder() {
        return new ErrorResponseBuilder();
    }

    // Getters
    public String getError() {
        return error;
    }

    public String getMessage() {
        return message;
    }

    public int getStatus() {
        return status;
    }

    public String getPath() {
        return path;
    }

    public LocalDateTime getTimestamp() {
        return timestamp;
    }

    public String getTraceId() {
        return traceId;
    }

    public List<ValidationError> getValidationErrors() {
        return validationErrors;
    }

    // Setters
    public void setError(String error) {
        this.error = error;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public void setTimestamp(LocalDateTime timestamp) {
        this.timestamp = timestamp;
    }

    public void setTraceId(String traceId) {
        this.traceId = traceId;
    }

    public void setValidationErrors(List<ValidationError> validationErrors) {
        this.validationErrors = validationErrors;
    }

    // equals method
    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        ErrorResponse that = (ErrorResponse) o;
        return status == that.status
                && Objects.equals(error, that.error)
                && Objects.equals(message, that.message)
                && Objects.equals(path, that.path)
                && Objects.equals(timestamp, that.timestamp)
                && Objects.equals(traceId, that.traceId)
                && Objects.equals(validationErrors, that.validationErrors);
    }

    // hashCode method
    @Override
    public int hashCode() {
        return Objects.hash(error, message, status, path, timestamp, traceId, validationErrors);
    }

    // toString method
    @Override
    public String toString() {
        return "ErrorResponse{"
                + "error='" + error + '\''
                + ", message='" + message + '\''
                + ", status=" + status
                + ", path='" + path + '\''
                + ", timestamp=" + timestamp
                + ", traceId='" + traceId + '\''
                + ", validationErrors=" + validationErrors
                + '}';
    }

    /**
     * Builder class for ErrorResponse
     */
    public static class ErrorResponseBuilder {

        private String error;
        private String message;
        private int status;
        private String path;
        private LocalDateTime timestamp;
        private String traceId;
        private List<ValidationError> validationErrors;

        public ErrorResponseBuilder error(String error) {
            this.error = error;
            return this;
        }

        public ErrorResponseBuilder message(String message) {
            this.message = message;
            return this;
        }

        public ErrorResponseBuilder status(int status) {
            this.status = status;
            return this;
        }

        public ErrorResponseBuilder path(String path) {
            this.path = path;
            return this;
        }

        public ErrorResponseBuilder timestamp(LocalDateTime timestamp) {
            this.timestamp = timestamp;
            return this;
        }

        public ErrorResponseBuilder traceId(String traceId) {
            this.traceId = traceId;
            return this;
        }

        public ErrorResponseBuilder validationErrors(List<ValidationError> validationErrors) {
            this.validationErrors = validationErrors;
            return this;
        }

        public ErrorResponse build() {
            return new ErrorResponse(error, message, status, path, timestamp, traceId, validationErrors);
        }
    }

    /**
     * Validation error details
     */
    public static class ValidationError {

        private String field;
        private Object rejectedValue;
        private String message;

        // Default constructor
        public ValidationError() {
        }

        // Constructor with all fields
        public ValidationError(String field, Object rejectedValue, String message) {
            this.field = field;
            this.rejectedValue = rejectedValue;
            this.message = message;
        }

        // Builder pattern implementation
        public static ValidationErrorBuilder builder() {
            return new ValidationErrorBuilder();
        }

        // Getters
        public String getField() {
            return field;
        }

        public Object getRejectedValue() {
            return rejectedValue;
        }

        public String getMessage() {
            return message;
        }

        // Setters
        public void setField(String field) {
            this.field = field;
        }

        public void setRejectedValue(Object rejectedValue) {
            this.rejectedValue = rejectedValue;
        }

        public void setMessage(String message) {
            this.message = message;
        }

        // equals method
        @Override
        public boolean equals(Object o) {
            if (this == o) {
                return true;
            }
            if (o == null || getClass() != o.getClass()) {
                return false;
            }
            ValidationError that = (ValidationError) o;
            return Objects.equals(field, that.field)
                    && Objects.equals(rejectedValue, that.rejectedValue)
                    && Objects.equals(message, that.message);
        }

        // hashCode method
        @Override
        public int hashCode() {
            return Objects.hash(field, rejectedValue, message);
        }

        // toString method
        @Override
        public String toString() {
            return "ValidationError{"
                    + "field='" + field + '\''
                    + ", rejectedValue=" + rejectedValue
                    + ", message='" + message + '\''
                    + '}';
        }

        /**
         * Builder class for ValidationError
         */
        public static class ValidationErrorBuilder {

            private String field;
            private Object rejectedValue;
            private String message;

            public ValidationErrorBuilder field(String field) {
                this.field = field;
                return this;
            }

            public ValidationErrorBuilder rejectedValue(Object rejectedValue) {
                this.rejectedValue = rejectedValue;
                return this;
            }

            public ValidationErrorBuilder message(String message) {
                this.message = message;
                return this;
            }

            public ValidationError build() {
                return new ValidationError(field, rejectedValue, message);
            }
        }
    }
}

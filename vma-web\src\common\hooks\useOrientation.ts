import { useEffect, useState } from 'react'

export function useOrientation() {
	const [isLandscape, setIsLandscape] = useState(() => matchMedia('(orientation: landscape)').matches)
	useEffect(() => {
		const mq = matchMedia('(orientation: landscape)')
		const handler = () => setIsLandscape(mq.matches)
		mq.addEventListener?.('change', handler)
		return () => mq.removeEventListener?.('change', handler)
	}, [])
	return { isLandscape }
}

package com.inspeedia.vanning;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

/**
 * Test to verify that the application starts up correctly without Lombok
 */
@SpringBootTest
@ActiveProfiles("test")
class ApplicationStartupTest {

    @Test
    void contextLoads() {
        // This test will pass if the application context loads successfully
        // It verifies that all beans can be created and autowired correctly
        // without Lombok dependencies
    }
}

package com.inspeedia.vanning.config;

import io.r2dbc.spi.ConnectionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.core.io.ClassPathResource;
import org.springframework.data.r2dbc.repository.config.EnableR2dbcRepositories;
import org.springframework.r2dbc.connection.init.ConnectionFactoryInitializer;
import org.springframework.r2dbc.connection.init.ResourceDatabasePopulator;
import org.springframework.r2dbc.core.DatabaseClient;

/**
 * Database configuration for R2DBC with MSSQL
 *
 * This configuration class sets up the reactive database connection and
 * provides beans for database operations.
 */
@Configuration
@EnableR2dbcRepositories(basePackages = "com.inspeedia.vanning.repository")
public class DatabaseConfig {

    private final Logger log = LoggerFactory.getLogger(DatabaseConfig.class);

    @Value("${spring.r2dbc.url}")
    private String databaseUrl;

    /**
     * Creates a DatabaseClient bean for custom database operations
     */
    @Bean
    public DatabaseClient databaseClient(ConnectionFactory connectionFactory) {
        return DatabaseClient.create(connectionFactory);
    }

    /**
     * Initializes the database with schema and data if needed Only runs in
     * development profile
     */
    @Bean
    @Profile("!test")
    public ConnectionFactoryInitializer initializer(ConnectionFactory connectionFactory) {
        ConnectionFactoryInitializer initializer = new ConnectionFactoryInitializer();
        initializer.setConnectionFactory(connectionFactory);

        // Initialize schema
        ResourceDatabasePopulator populator = new ResourceDatabasePopulator();
        populator.addScript(new ClassPathResource("schema.sql"));
        populator.addScript(new ClassPathResource("data.sql"));

        initializer.setDatabasePopulator(populator);

        log.info("Database initializer configured for connection: {}", databaseUrl);
        return initializer;
    }
}

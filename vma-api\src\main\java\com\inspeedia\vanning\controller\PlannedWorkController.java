package com.inspeedia.vanning.controller;

import com.inspeedia.vanning.domain.PlannedWork;
import com.inspeedia.vanning.service.PlannedWorkService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.LocalDate;
import java.time.LocalTime;

/**
 * REST Controller for PlannedWork operations
 *
 * This controller provides reactive REST endpoints for managing planned work in
 * the VMA (Vanning Management Application) system.
 */
@RestController
@RequestMapping("${app.api.base-path}/planned-work")
@Tag(name = "Planned Work Management", description = "APIs for managing planned work records")
public class PlannedWorkController {

    private final Logger log = LoggerFactory.getLogger(PlannedWorkController.class);
    private final PlannedWorkService plannedWorkService;

    public PlannedWorkController(PlannedWorkService plannedWorkService) {
        this.plannedWorkService = plannedWorkService;
    }

    @Operation(summary = "Create a new planned work record", description = "Creates a new planned work record in the system")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "201", description = "Planned work created successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid planned work data")
    })
    @PostMapping
    public Mono<ResponseEntity<PlannedWork>> createPlannedWork(@Valid @RequestBody PlannedWork plannedWork) {
        log.info("Creating new planned work for date: {}", plannedWork.getDate());

        return plannedWorkService.createPlannedWork(plannedWork)
                .map(createdWork -> ResponseEntity.status(HttpStatus.CREATED).body(createdWork))
                .onErrorReturn(IllegalArgumentException.class, ResponseEntity.badRequest().build());
    }

    @Operation(summary = "Update a planned work record", description = "Updates an existing planned work record")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Planned work updated successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid planned work data"),
        @ApiResponse(responseCode = "404", description = "Planned work not found")
    })
    @PutMapping("/{id}")
    public Mono<ResponseEntity<PlannedWork>> updatePlannedWork(
            @Parameter(description = "Planned work ID") @PathVariable Long id,
            @Valid @RequestBody PlannedWork plannedWork) {
        log.info("Updating planned work with ID: {}", id);

        return plannedWorkService.updatePlannedWork(id, plannedWork)
                .map(updatedWork -> ResponseEntity.ok(updatedWork))
                .onErrorReturn(IllegalArgumentException.class, ResponseEntity.notFound().build());
    }

    @Operation(summary = "Get planned work by ID", description = "Retrieves a planned work record by its ID")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Planned work found"),
        @ApiResponse(responseCode = "404", description = "Planned work not found")
    })
    @GetMapping("/{id}")
    public Mono<ResponseEntity<PlannedWork>> getPlannedWorkById(
            @Parameter(description = "Planned work ID") @PathVariable Long id) {
        log.info("Fetching planned work with ID: {}", id);

        return plannedWorkService.getPlannedWorkById(id)
                .map(work -> ResponseEntity.ok(work))
                .onErrorReturn(IllegalArgumentException.class, ResponseEntity.notFound().build());
    }

    @Operation(summary = "Get all active planned work", description = "Retrieves all active planned work records with pagination")
    @GetMapping
    public Flux<PlannedWork> getAllActivePlannedWork(
            @Parameter(description = "Page number (0-based)") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Page size") @RequestParam(defaultValue = "20") int size) {
        log.info("Fetching active planned work - page: {}, size: {}", page, size);

        return plannedWorkService.getAllActivePlannedWork(page, size);
    }

    @Operation(summary = "Get planned work by date", description = "Retrieves planned work records by date")
    @GetMapping("/date/{date}")
    public Flux<PlannedWork> getPlannedWorkByDate(
            @Parameter(description = "Date (yyyy-MM-dd)") @PathVariable @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate date) {
        log.info("Fetching planned work for date: {}", date);

        return plannedWorkService.getPlannedWorkByDate(date);
    }

    @Operation(summary = "Get planned work by date range", description = "Retrieves planned work records by date range")
    @GetMapping("/date-range")
    public Flux<PlannedWork> getPlannedWorkByDateRange(
            @Parameter(description = "Start date") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @Parameter(description = "End date") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        log.info("Fetching planned work between {} and {}", startDate, endDate);

        return plannedWorkService.getPlannedWorkByDateRange(startDate, endDate);
    }

    @Operation(summary = "Get planned work by van GP", description = "Retrieves planned work records by van GP")
    @GetMapping("/van-gp/{vanGp}")
    public Flux<PlannedWork> getPlannedWorkByVanGp(
            @Parameter(description = "Van GP (2 uppercase letters)") @PathVariable String vanGp) {
        log.info("Fetching planned work for van GP: {}", vanGp);

        return plannedWorkService.getPlannedWorkByVanGp(vanGp);
    }

    @Operation(summary = "Get planned work by delivery platform", description = "Retrieves planned work records by delivery platform")
    @GetMapping("/delivery-platform/{deliveryPlatform}")
    public Flux<PlannedWork> getPlannedWorkByDeliveryPlatform(
            @Parameter(description = "Delivery platform (1 uppercase letter)") @PathVariable String deliveryPlatform) {
        log.info("Fetching planned work for delivery platform: {}", deliveryPlatform);

        return plannedWorkService.getPlannedWorkByDeliveryPlatform(deliveryPlatform);
    }

    @Operation(summary = "Get planned work by collection platform", description = "Retrieves planned work records by collection platform")
    @GetMapping("/collection-platform/{collectionPlatform}")
    public Flux<PlannedWork> getPlannedWorkByCollectionPlatform(
            @Parameter(description = "Collection platform (1 uppercase letter)") @PathVariable String collectionPlatform) {
        log.info("Fetching planned work for collection platform: {}", collectionPlatform);

        return plannedWorkService.getPlannedWorkByCollectionPlatform(collectionPlatform);
    }

    @Operation(summary = "Get planned work by size", description = "Retrieves planned work records by size")
    @GetMapping("/size/{size}")
    public Flux<PlannedWork> getPlannedWorkBySize(
            @Parameter(description = "Size (2 alphanumeric characters)") @PathVariable String size) {
        log.info("Fetching planned work for size: {}", size);

        return plannedWorkService.getPlannedWorkBySize(size);
    }

    @Operation(summary = "Get today's planned work", description = "Retrieves today's planned work records")
    @GetMapping("/today")
    public Flux<PlannedWork> getTodaysPlannedWork() {
        log.info("Fetching today's planned work");

        return plannedWorkService.getTodaysPlannedWork();
    }

    @Operation(summary = "Get upcoming planned work", description = "Retrieves upcoming planned work records")
    @GetMapping("/upcoming")
    public Flux<PlannedWork> getUpcomingPlannedWork() {
        log.info("Fetching upcoming planned work");

        return plannedWorkService.getUpcomingPlannedWork();
    }

    @Operation(summary = "Get planned work by start time range", description = "Retrieves planned work records by start time range")
    @GetMapping("/start-time-range")
    public Flux<PlannedWork> getPlannedWorkByStartTimeRange(
            @Parameter(description = "Start time") @RequestParam @DateTimeFormat(pattern = "HH:mm") LocalTime startTime,
            @Parameter(description = "End time") @RequestParam @DateTimeFormat(pattern = "HH:mm") LocalTime endTime) {
        log.info("Fetching planned work with start time between {} and {}", startTime, endTime);

        return plannedWorkService.getPlannedWorkByStartTimeRange(startTime, endTime);
    }

    @Operation(summary = "Delete a planned work record", description = "Soft deletes a planned work record")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "204", description = "Planned work deleted successfully"),
        @ApiResponse(responseCode = "404", description = "Planned work not found")
    })
    @DeleteMapping("/{id}")
    public Mono<ResponseEntity<Void>> deletePlannedWork(
            @Parameter(description = "Planned work ID") @PathVariable Long id) {
        log.info("Deleting planned work with ID: {}", id);

        return plannedWorkService.deletePlannedWork(id)
                .then(Mono.just(ResponseEntity.noContent().<Void>build()))
                .onErrorReturn(IllegalArgumentException.class, ResponseEntity.notFound().build());
    }

    @Operation(summary = "Count planned work by van GP", description = "Returns the count of planned work records by van GP")
    @GetMapping("/count/van-gp/{vanGp}")
    public Mono<Long> countPlannedWorkByVanGp(
            @Parameter(description = "Van GP") @PathVariable String vanGp) {
        log.info("Counting planned work for van GP: {}", vanGp);

        return plannedWorkService.countPlannedWorkByVanGp(vanGp);
    }
}

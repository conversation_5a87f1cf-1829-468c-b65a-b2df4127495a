<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.inspeedia.vanning.controller.TaskControllerTest" tests="4" skipped="0" failures="0" errors="0" timestamp="2025-09-02T05:14:31" hostname="MSI" time="0.903">
  <properties/>
  <testcase name="testGetAllTasks()" classname="com.inspeedia.vanning.controller.TaskControllerTest" time="0.853"/>
  <testcase name="testGetAllTasksEmpty()" classname="com.inspeedia.vanning.controller.TaskControllerTest" time="0.022"/>
  <testcase name="testGetTodaysTasks()" classname="com.inspeedia.vanning.controller.TaskControllerTest" time="0.01"/>
  <testcase name="testGetTodaysTasksEmpty()" classname="com.inspeedia.vanning.controller.TaskControllerTest" time="0.006"/>
  <system-out><![CDATA[{"@timestamp":"2025-09-02T10:44:32.5709754+05:30","level":"INFO","logger_name":"com.inspeedia.vanning.controller.TaskController","message":"Fetching all tasks"}
{"@timestamp":"2025-09-02T10:44:32.8600602+05:30","level":"INFO","logger_name":"com.inspeedia.vanning.controller.TaskController","message":"Fetching all tasks"}
{"@timestamp":"2025-09-02T10:44:32.880148+05:30","level":"INFO","logger_name":"com.inspeedia.vanning.controller.TaskController","message":"Fetching today's tasks"}
{"@timestamp":"2025-09-02T10:44:32.8909853+05:30","level":"INFO","logger_name":"com.inspeedia.vanning.controller.TaskController","message":"Fetching today's tasks"}
]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>

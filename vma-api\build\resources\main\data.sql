-- Sample data for VMA (Vanning Management Application) API
-- This file contains sample data for development and testing

-- Insert sample actual work records
INSERT INTO actual_work (user_name, start_time, end_time, duration, progress, progress_rate, created_by, updated_by)
VALUES
('田中太郎', '08:00:00', '12:00:00', '04:00:00', 8, 80.0, 'system', 'system'),
('佐藤花子', '09:30:00', '17:30:00', '08:00:00', 10, 100.0, 'system', 'system'),
('山田一郎', '07:00:00', '15:00:00', '08:00:00', 7, 70.0, 'system', 'system'),
('鈴木雪', '10:00:00', '14:30:00', '04:30:00', 6, 60.0, 'system', 'system'),
('渡辺健二', '08:30:00', '16:30:00', '08:00:00', 9, 90.0, 'system', 'system');

-- Insert sample planned work records
INSERT INTO planned_work (date, van_gp, delivery_platform, collection_platform, load_time, size, start_time, end_time, duration, created_by, updated_by)
VALUES
('2024-01-15', 'VG', 'A', 'B', '08:00:00', '4H', '08:30:00', '16:30:00', '08:00:00', 'system', 'system'),
('2024-01-16', 'OU', 'B', 'C', '09:00:00', '20', '09:30:00', '17:30:00', '08:00:00', 'system', 'system'),
('2024-01-17', 'US', 'C', 'A', '07:30:00', '4H', '08:00:00', '16:00:00', '08:00:00', 'system', 'system'),
('2024-01-18', 'KN', 'L', 'B', '10:00:00', '20', '10:30:00', '18:30:00', '08:00:00', 'system', 'system'),
('2024-01-19', 'VG', 'A', 'L', '08:15:00', '4H', '08:45:00', '16:45:00', '08:00:00', 'system', 'system');

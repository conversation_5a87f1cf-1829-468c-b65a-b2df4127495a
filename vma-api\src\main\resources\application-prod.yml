# Production Profile Configuration
spring:
  r2dbc:
    url: r2dbc:mssql://${DB_HOST:localhost}:${DB_PORT:1433}/${DB_NAME:vma_db}
    username: ${DB_USERNAME}
    password: ${DB_PASSWORD}
    pool:
      initial-size: 10
      max-size: 50
      max-idle-time: 10m

logging:
  level:
    root: WARN
    com.inspeedia.vanning: INFO
    org.springframework.r2dbc: WARN
  file:
    name: /var/log/vma-api/application.log

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus

# Strict CORS for production
app:
  cors:
    allowed-origins:
      - "https://yourdomain.com"
      - "https://www.yourdomain.com"

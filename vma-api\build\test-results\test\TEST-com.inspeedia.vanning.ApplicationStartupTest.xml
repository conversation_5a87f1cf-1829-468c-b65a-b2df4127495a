<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.inspeedia.vanning.ApplicationStartupTest" tests="1" skipped="0" failures="0" errors="0" timestamp="2025-09-01T12:10:44" hostname="MSI" time="0.149">
  <properties/>
  <testcase name="contextLoads()" classname="com.inspeedia.vanning.ApplicationStartupTest" time="0.149"/>
  <system-out><![CDATA[{"@timestamp":"2025-09-01T17:40:34.1983527+05:30","level":"INFO","logger_name":"org.springframework.test.context.support.AnnotationConfigContextLoaderUtils","message":"Could not detect default configuration classes for test class [com.inspeedia.vanning.ApplicationStartupTest]: ApplicationStartupTest does not declare any static, non-private, non-final, nested classes annotated with @Configuration."}
{"@timestamp":"2025-09-01T17:40:34.2325306+05:30","level":"INFO","logger_name":"org.springframework.boot.test.context.SpringBootTestContextBootstrapper","message":"Found @SpringBootConfiguration com.inspeedia.vanning.VmApplication for test class com.inspeedia.vanning.ApplicationStartupTest"}

  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/

 :: Spring Boot ::                (v3.5.5)

{"@timestamp":"2025-09-01T17:40:34.488471+05:30","level":"INFO","logger_name":"com.inspeedia.vanning.ApplicationStartupTest","message":"Starting ApplicationStartupTest using Java 17.0.14 with PID 8456 (started by INSP_MSS in C:\\Dev\\Mohan\\Projects\\MSS\\Toyutsu\\vanning\\vma-api)"}
{"@timestamp":"2025-09-01T17:40:34.4904194+05:30","level":"INFO","logger_name":"com.inspeedia.vanning.ApplicationStartupTest","message":"The following 1 profile is active: \"test\""}
{"@timestamp":"2025-09-01T17:40:42.7552724+05:30","level":"INFO","logger_name":"com.inspeedia.vanning.config.SecurityConfig","message":"Configured in-memory users: admin, user"}
{"@timestamp":"2025-09-01T17:40:43.6651242+05:30","level":"INFO","logger_name":"com.inspeedia.vanning.config.WebConfig","message":"CORS configured with origins: [*], methods: [*]"}
{"@timestamp":"2025-09-01T17:40:44.868497+05:30","level":"INFO","logger_name":"com.inspeedia.vanning.ApplicationStartupTest","message":"Started ApplicationStartupTest in 10.601 seconds (process running for 16.974)"}
]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>

package com.inspeedia.vanning.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * Application configuration properties
 *
 * This class maps the application-specific configuration properties from
 * application.yml to Java objects.
 */
@Component
@ConfigurationProperties(prefix = "app")
public class AppProperties {

    private Cors cors = new Cors();
    private Api api = new Api();
    private Pagination pagination = new Pagination();

    // Default constructor
    public AppProperties() {
    }

    // Constructor with all fields
    public AppProperties(Cors cors, Api api, Pagination pagination) {
        this.cors = cors;
        this.api = api;
        this.pagination = pagination;
    }

    // Getters
    public Cors getCors() {
        return cors;
    }

    public Api getApi() {
        return api;
    }

    public Pagination getPagination() {
        return pagination;
    }

    // Setters
    public void setCors(Cors cors) {
        this.cors = cors;
    }

    public void setApi(Api api) {
        this.api = api;
    }

    public void setPagination(Pagination pagination) {
        this.pagination = pagination;
    }

    // equals method
    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        AppProperties that = (AppProperties) o;
        return Objects.equals(cors, that.cors)
                && Objects.equals(api, that.api)
                && Objects.equals(pagination, that.pagination);
    }

    // hashCode method
    @Override
    public int hashCode() {
        return Objects.hash(cors, api, pagination);
    }

    // toString method
    @Override
    public String toString() {
        return "AppProperties{"
                + "cors=" + cors
                + ", api=" + api
                + ", pagination=" + pagination
                + '}';
    }

    public static class Cors {

        private List<String> allowedOrigins;
        private List<String> allowedMethods;
        private List<String> allowedHeaders;
        private boolean allowCredentials;
        private long maxAge;

        // Default constructor
        public Cors() {
        }

        // Constructor with all fields
        public Cors(List<String> allowedOrigins, List<String> allowedMethods, List<String> allowedHeaders,
                boolean allowCredentials, long maxAge) {
            this.allowedOrigins = allowedOrigins;
            this.allowedMethods = allowedMethods;
            this.allowedHeaders = allowedHeaders;
            this.allowCredentials = allowCredentials;
            this.maxAge = maxAge;
        }

        // Getters
        public List<String> getAllowedOrigins() {
            return allowedOrigins;
        }

        public List<String> getAllowedMethods() {
            return allowedMethods;
        }

        public List<String> getAllowedHeaders() {
            return allowedHeaders;
        }

        public boolean isAllowCredentials() {
            return allowCredentials;
        }

        public long getMaxAge() {
            return maxAge;
        }

        // Setters
        public void setAllowedOrigins(List<String> allowedOrigins) {
            this.allowedOrigins = allowedOrigins;
        }

        public void setAllowedMethods(List<String> allowedMethods) {
            this.allowedMethods = allowedMethods;
        }

        public void setAllowedHeaders(List<String> allowedHeaders) {
            this.allowedHeaders = allowedHeaders;
        }

        public void setAllowCredentials(boolean allowCredentials) {
            this.allowCredentials = allowCredentials;
        }

        public void setMaxAge(long maxAge) {
            this.maxAge = maxAge;
        }

        // equals method
        @Override
        public boolean equals(Object o) {
            if (this == o) {
                return true;
            }
            if (o == null || getClass() != o.getClass()) {
                return false;
            }
            Cors cors = (Cors) o;
            return allowCredentials == cors.allowCredentials
                    && maxAge == cors.maxAge
                    && Objects.equals(allowedOrigins, cors.allowedOrigins)
                    && Objects.equals(allowedMethods, cors.allowedMethods)
                    && Objects.equals(allowedHeaders, cors.allowedHeaders);
        }

        // hashCode method
        @Override
        public int hashCode() {
            return Objects.hash(allowedOrigins, allowedMethods, allowedHeaders, allowCredentials, maxAge);
        }

        // toString method
        @Override
        public String toString() {
            return "Cors{"
                    + "allowedOrigins=" + allowedOrigins
                    + ", allowedMethods=" + allowedMethods
                    + ", allowedHeaders=" + allowedHeaders
                    + ", allowCredentials=" + allowCredentials
                    + ", maxAge=" + maxAge
                    + '}';
        }
    }

    public static class Api {

        private String version;
        private String basePath;

        // Default constructor
        public Api() {
        }

        // Constructor with all fields
        public Api(String version, String basePath) {
            this.version = version;
            this.basePath = basePath;
        }

        // Getters
        public String getVersion() {
            return version;
        }

        public String getBasePath() {
            return basePath;
        }

        // Setters
        public void setVersion(String version) {
            this.version = version;
        }

        public void setBasePath(String basePath) {
            this.basePath = basePath;
        }

        // equals method
        @Override
        public boolean equals(Object o) {
            if (this == o) {
                return true;
            }
            if (o == null || getClass() != o.getClass()) {
                return false;
            }
            Api api = (Api) o;
            return Objects.equals(version, api.version)
                    && Objects.equals(basePath, api.basePath);
        }

        // hashCode method
        @Override
        public int hashCode() {
            return Objects.hash(version, basePath);
        }

        // toString method
        @Override
        public String toString() {
            return "Api{"
                    + "version='" + version + '\''
                    + ", basePath='" + basePath + '\''
                    + '}';
        }
    }

    public static class Pagination {

        private int defaultPageSize = 20;
        private int maxPageSize = 100;

        // Default constructor
        public Pagination() {
        }

        // Constructor with all fields
        public Pagination(int defaultPageSize, int maxPageSize) {
            this.defaultPageSize = defaultPageSize;
            this.maxPageSize = maxPageSize;
        }

        // Getters
        public int getDefaultPageSize() {
            return defaultPageSize;
        }

        public int getMaxPageSize() {
            return maxPageSize;
        }

        // Setters
        public void setDefaultPageSize(int defaultPageSize) {
            this.defaultPageSize = defaultPageSize;
        }

        public void setMaxPageSize(int maxPageSize) {
            this.maxPageSize = maxPageSize;
        }

        // equals method
        @Override
        public boolean equals(Object o) {
            if (this == o) {
                return true;
            }
            if (o == null || getClass() != o.getClass()) {
                return false;
            }
            Pagination that = (Pagination) o;
            return defaultPageSize == that.defaultPageSize
                    && maxPageSize == that.maxPageSize;
        }

        // hashCode method
        @Override
        public int hashCode() {
            return Objects.hash(defaultPageSize, maxPageSize);
        }

        // toString method
        @Override
        public String toString() {
            return "Pagination{"
                    + "defaultPageSize=" + defaultPageSize
                    + ", maxPageSize=" + maxPageSize
                    + '}';
        }
    }
}

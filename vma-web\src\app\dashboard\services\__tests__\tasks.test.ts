/**
 * Tests for tasks service
 */

import { fetchTasks } from '../tasks'
import { mockTasks } from '../../../../__mocks__/mockData'

// Mock the httpClient module
jest.mock('../../../../core/api/httpClient', () => ({
	httpGet: jest.fn(),
}))

// Mock the endpoints module
jest.mock('../../../../core/api/endpoints', () => ({
	endpoints: {
		tasks: '/api/tasks.json',
	},
}))

import { httpGet } from '../../../../core/api/httpClient'
import { endpoints } from '../../../../core/api/endpoints'

const mockHttpGet = httpGet as jest.MockedFunction<typeof httpGet>

describe('Tasks Service', () => {
	beforeEach(() => {
		jest.clearAllMocks()
	})

	describe('fetchTasks', () => {
		it('should fetch tasks successfully', async () => {
			// Arrange
			mockHttpGet.mockResolvedValue(mockTasks)

			// Act
			const result = await fetchTasks()

			// Assert
			expect(result).toEqual(mockTasks)
			expect(mockHttpGet).toHaveBeenCalledWith(endpoints.tasks, {
				headers: { 'cache-control': 'no-cache' },
			})
			expect(mockHttpGet).toHaveBeenCalledTimes(1)
		})

		it('should fetch tasks with abort signal', async () => {
			// Arrange
			const abortController = new AbortController()
			const signal = abortController.signal
			mockHttpGet.mockResolvedValue(mockTasks)

			// Act
			const result = await fetchTasks(signal)

			// Assert
			expect(result).toEqual(mockTasks)
			expect(mockHttpGet).toHaveBeenCalledWith(endpoints.tasks, {
				signal,
				headers: { 'cache-control': 'no-cache' },
			})
		})

		it('should handle HTTP errors', async () => {
			// Arrange
			const errorMessage = 'HTTP 500'
			mockHttpGet.mockRejectedValue(new Error(errorMessage))

			// Act & Assert
			await expect(fetchTasks()).rejects.toThrow(errorMessage)
			expect(mockHttpGet).toHaveBeenCalledWith(endpoints.tasks, {
				headers: { 'cache-control': 'no-cache' },
			})
		})

		it('should handle network errors', async () => {
			// Arrange
			const networkError = new Error('Network error')
			mockHttpGet.mockRejectedValue(networkError)

			// Act & Assert
			await expect(fetchTasks()).rejects.toThrow('Network error')
		})

		it('should handle empty response', async () => {
			// Arrange
			mockHttpGet.mockResolvedValue([])

			// Act
			const result = await fetchTasks()

			// Assert
			expect(result).toEqual([])
			expect(mockHttpGet).toHaveBeenCalledWith(endpoints.tasks, {
				headers: { 'cache-control': 'no-cache' },
			})
		})

		it('should handle null response', async () => {
			// Arrange
			// eslint-disable-next-line @typescript-eslint/no-explicit-any
			mockHttpGet.mockResolvedValue(null as any)

			// Act
			const result = await fetchTasks()

			// Assert
			expect(result).toBeNull()
		})
	})
})

<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<meta http-equiv="x-ua-compatible" content="IE=edge"/>
<title>Test results - TaskServiceTest</title>
<link href="../css/base-style.css" rel="stylesheet" type="text/css"/>
<link href="../css/style.css" rel="stylesheet" type="text/css"/>
<script src="../js/report.js" type="text/javascript"></script>
</head>
<body>
<div id="content">
<h1>TaskServiceTest</h1>
<div class="breadcrumbs">
<a href="../index.html">all</a> &gt; 
<a href="../packages/com.inspeedia.vanning.service.html">com.inspeedia.vanning.service</a> &gt; TaskServiceTest</div>
<div id="summary">
<table>
<tr>
<td>
<div class="summaryGroup">
<table>
<tr>
<td>
<div class="infoBox" id="tests">
<div class="counter">4</div>
<p>tests</p>
</div>
</td>
<td>
<div class="infoBox" id="failures">
<div class="counter">0</div>
<p>failures</p>
</div>
</td>
<td>
<div class="infoBox" id="ignored">
<div class="counter">0</div>
<p>ignored</p>
</div>
</td>
<td>
<div class="infoBox" id="duration">
<div class="counter">1.088s</div>
<p>duration</p>
</div>
</td>
</tr>
</table>
</div>
</td>
<td>
<div class="infoBox success" id="successRate">
<div class="percent">100%</div>
<p>successful</p>
</div>
</td>
</tr>
</table>
</div>
<div id="tabs">
<ul class="tabLinks">
<li>
<a href="#tab0">Tests</a>
</li>
<li>
<a href="#tab1">Standard output</a>
</li>
</ul>
<div id="tab0" class="tab">
<h2>Tests</h2>
<table>
<thead>
<tr>
<th>Test</th>
<th>Duration</th>
<th>Result</th>
</tr>
</thead>
<tr>
<td class="success">testGetAllTasks()</td>
<td class="success">0.014s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testGetAllTasksEmpty()</td>
<td class="success">0.017s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testGetAllTasksWithoutActualWork()</td>
<td class="success">1.041s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testGetTodaysTasks()</td>
<td class="success">0.016s</td>
<td class="success">passed</td>
</tr>
</table>
</div>
<div id="tab1" class="tab">
<h2>Standard output</h2>
<span class="code">
<pre>{&quot;@timestamp&quot;:&quot;2025-09-01T17:30:36.5939153+05:30&quot;,&quot;level&quot;:&quot;DEBUG&quot;,&quot;logger_name&quot;:&quot;com.inspeedia.vanning.service.TaskService&quot;,&quot;message&quot;:&quot;Fetching all tasks by combining planned and actual work&quot;}
{&quot;@timestamp&quot;:&quot;2025-09-01T17:30:36.7258382+05:30&quot;,&quot;level&quot;:&quot;DEBUG&quot;,&quot;logger_name&quot;:&quot;com.inspeedia.vanning.service.TaskService&quot;,&quot;message&quot;:&quot;Fetching all tasks by combining planned and actual work&quot;}
{&quot;@timestamp&quot;:&quot;2025-09-01T17:30:36.7395575+05:30&quot;,&quot;level&quot;:&quot;DEBUG&quot;,&quot;logger_name&quot;:&quot;com.inspeedia.vanning.service.TaskService&quot;,&quot;message&quot;:&quot;Fetching all tasks by combining planned and actual work&quot;}
{&quot;@timestamp&quot;:&quot;2025-09-01T17:30:36.7574179+05:30&quot;,&quot;level&quot;:&quot;DEBUG&quot;,&quot;logger_name&quot;:&quot;com.inspeedia.vanning.service.TaskService&quot;,&quot;message&quot;:&quot;Fetching today's tasks&quot;}
</pre>
</span>
</div>
</div>
<div id="footer">
<p>
<div>
<label class="hidden" id="label-for-line-wrapping-toggle" for="line-wrapping-toggle">Wrap lines
<input id="line-wrapping-toggle" type="checkbox" autocomplete="off"/>
</label>
</div>Generated by 
<a href="http://www.gradle.org">Gradle 8.5</a> at Sep 1, 2025, 5:30:36 PM</p>
</div>
</div>
</body>
</html>

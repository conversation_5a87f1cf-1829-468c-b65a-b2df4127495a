-- Database schema for VMA (Vanning Management Application) API
-- MSSQL Server compatible schema

-- Create actual_work table
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='actual_work' AND xtype='U')
CREATE TABLE actual_work (
    id BIGINT IDENTITY(1,1) PRIMARY KEY,
    user_name NVARCHAR(100) NOT NULL,
    start_time TIME NOT NULL,
    end_time TIME NOT NULL,
    duration TIME NOT NULL,
    progress INT NOT NULL CHECK (progress >= 0 AND progress <= 10),
    progress_rate FLOAT NOT NULL CHECK (progress_rate >= 0.0 AND progress_rate <= 100.0),
    created_at DATETIME2 DEFAULT GETDATE(),
    updated_at DATETIME2 DEFAULT GETDATE(),
    created_by NVARCHAR(50),
    updated_by <PERSON>VARCHAR(50),
    version BIGINT DEFAULT 0,
    deleted BIT DEFAULT 0
);

-- Create planned_work table
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='planned_work' AND xtype='U')
CREATE TABLE planned_work (
    id BIGINT IDENTITY(1,1) PRIMARY KEY,
    date DATE NOT NULL,
    van_gp NVARCHAR(2) NOT NULL CHECK (LEN(van_gp) = 2 AND van_gp = UPPER(van_gp)),
    delivery_platform NVARCHAR(1) NOT NULL CHECK (LEN(delivery_platform) = 1 AND delivery_platform = UPPER(delivery_platform)),
    collection_platform NVARCHAR(1) NOT NULL CHECK (LEN(collection_platform) = 1 AND collection_platform = UPPER(collection_platform)),
    load_time TIME NOT NULL,
    size NVARCHAR(2) NOT NULL CHECK (LEN(size) = 2),
    start_time TIME NOT NULL,
    end_time TIME NOT NULL,
    duration TIME NOT NULL,
    created_at DATETIME2 DEFAULT GETDATE(),
    updated_at DATETIME2 DEFAULT GETDATE(),
    created_by NVARCHAR(50),
    updated_by NVARCHAR(50),
    version BIGINT DEFAULT 0,
    deleted BIT DEFAULT 0
);

-- Create indexes for better performance
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_actual_work_user_name' AND object_id = OBJECT_ID('actual_work'))
CREATE INDEX IX_actual_work_user_name ON actual_work(user_name) WHERE deleted = 0;

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_actual_work_start_time' AND object_id = OBJECT_ID('actual_work'))
CREATE INDEX IX_actual_work_start_time ON actual_work(start_time) WHERE deleted = 0;

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_actual_work_progress' AND object_id = OBJECT_ID('actual_work'))
CREATE INDEX IX_actual_work_progress ON actual_work(progress) WHERE deleted = 0;

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_actual_work_progress_rate' AND object_id = OBJECT_ID('actual_work'))
CREATE INDEX IX_actual_work_progress_rate ON actual_work(progress_rate) WHERE deleted = 0;

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_planned_work_date' AND object_id = OBJECT_ID('planned_work'))
CREATE INDEX IX_planned_work_date ON planned_work(date) WHERE deleted = 0;

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_planned_work_van_gp' AND object_id = OBJECT_ID('planned_work'))
CREATE INDEX IX_planned_work_van_gp ON planned_work(van_gp) WHERE deleted = 0;

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_planned_work_delivery_platform' AND object_id = OBJECT_ID('planned_work'))
CREATE INDEX IX_planned_work_delivery_platform ON planned_work(delivery_platform) WHERE deleted = 0;

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_planned_work_collection_platform' AND object_id = OBJECT_ID('planned_work'))
CREATE INDEX IX_planned_work_collection_platform ON planned_work(collection_platform) WHERE deleted = 0;

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_planned_work_start_time' AND object_id = OBJECT_ID('planned_work'))
CREATE INDEX IX_planned_work_start_time ON planned_work(start_time) WHERE deleted = 0;

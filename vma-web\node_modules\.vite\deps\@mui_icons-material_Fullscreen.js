"use client";
import "./chunk-C6WWHQR7.js";
import {
  createSvgIcon
} from "./chunk-J3L5TEQV.js";
import "./chunk-NINHBGFJ.js";
import {
  require_jsx_runtime
} from "./chunk-TG2L3OCW.js";
import "./chunk-AFOL7T4F.js";
import {
  __toESM
} from "./chunk-PR4QN5HX.js";

// node_modules/@mui/icons-material/esm/Fullscreen.js
var import_jsx_runtime = __toESM(require_jsx_runtime());
var Fullscreen_default = createSvgIcon((0, import_jsx_runtime.jsx)("path", {
  d: "M7 14H5v5h5v-2H7zm-2-4h2V7h3V5H5zm12 7h-3v2h5v-5h-2zM14 5v2h3v3h2V5z"
}), "Fullscreen");
export {
  Fullscreen_default as default
};
//# sourceMappingURL=@mui_icons-material_Fullscreen.js.map

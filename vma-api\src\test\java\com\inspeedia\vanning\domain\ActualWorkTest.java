package com.inspeedia.vanning.domain;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import static org.junit.jupiter.api.Assertions.*;

import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * Unit tests for ActualWork entity
 */
class ActualWorkTest {

    private ActualWork actualWork;
    private LocalTime startTime;
    private LocalTime endTime;
    private LocalTime duration;

    @BeforeEach
    @SuppressWarnings("unused")
    void setUp() {
        startTime = LocalTime.of(8, 0);
        endTime = LocalTime.of(17, 0);
        duration = LocalTime.of(9, 0);
        
        actualWork = new ActualWork();
        actualWork.setUserName("田中太郎");
        actualWork.setStartTime(startTime);
        actualWork.setEndTime(endTime);
        actualWork.setDuration(duration);
        actualWork.setProgress(8);
        actualWork.setProgressRate(80.0f);
    }

    @Test
    void testDefaultConstructor() {
        ActualWork work = new ActualWork();
        assertNotNull(work);
        assertNull(work.getUserName());
        assertNull(work.getStartTime());
        assertNull(work.getEndTime());
        assertNull(work.getDuration());
        assertNull(work.getProgress());
        assertNull(work.getProgressRate());
    }

    @Test
    void testParameterizedConstructor() {
        ActualWork work = new ActualWork("佐藤花子", startTime, endTime, duration, 10, 100.0f);
        
        assertEquals("佐藤花子", work.getUserName());
        assertEquals(startTime, work.getStartTime());
        assertEquals(endTime, work.getEndTime());
        assertEquals(duration, work.getDuration());
        assertEquals(10, work.getProgress());
        assertEquals(100.0f, work.getProgressRate());
    }

    @Test
    void testFullConstructor() {
        LocalDateTime now = LocalDateTime.now();
        ActualWork work = new ActualWork(1L, now, now, 1L, "admin", "admin", false,
                "山田一郎", startTime, endTime, duration, 7, 70.0f);
        
        assertEquals(1L, work.getId());
        assertEquals(now, work.getCreatedAt());
        assertEquals(now, work.getUpdatedAt());
        assertEquals(1L, work.getVersion());
        assertEquals("admin", work.getCreatedBy());
        assertEquals("admin", work.getUpdatedBy());
        assertFalse(work.isDeleted());
        assertEquals("山田一郎", work.getUserName());
        assertEquals(startTime, work.getStartTime());
        assertEquals(endTime, work.getEndTime());
        assertEquals(duration, work.getDuration());
        assertEquals(7, work.getProgress());
        assertEquals(70.0f, work.getProgressRate());
    }

    @Test
    void testGettersAndSetters() {
        assertEquals("田中太郎", actualWork.getUserName());
        assertEquals(startTime, actualWork.getStartTime());
        assertEquals(endTime, actualWork.getEndTime());
        assertEquals(duration, actualWork.getDuration());
        assertEquals(8, actualWork.getProgress());
        assertEquals(80.0f, actualWork.getProgressRate());

        // Test setters
        actualWork.setUserName("新しいユーザー");
        actualWork.setProgress(9);
        actualWork.setProgressRate(90.0f);

        assertEquals("新しいユーザー", actualWork.getUserName());
        assertEquals(9, actualWork.getProgress());
        assertEquals(90.0f, actualWork.getProgressRate());
    }

    @Test
    void testEquals() {
        ActualWork work1 = new ActualWork("田中太郎", startTime, endTime, duration, 8, 80.0f);
        ActualWork work2 = new ActualWork("田中太郎", startTime, endTime, duration, 8, 80.0f);
        ActualWork work3 = new ActualWork("佐藤花子", startTime, endTime, duration, 8, 80.0f);

        assertEquals(work1, work2);
        assertNotEquals(work1, work3);
        assertNotEquals(work1, null);
        assertNotEquals(work1, "string");
    }

    @Test
    void testHashCode() {
        ActualWork work1 = new ActualWork("田中太郎", startTime, endTime, duration, 8, 80.0f);
        ActualWork work2 = new ActualWork("田中太郎", startTime, endTime, duration, 8, 80.0f);

        assertEquals(work1.hashCode(), work2.hashCode());
    }

    @Test
    void testToString() {
        String toString = actualWork.toString();
        
        assertNotNull(toString);
        assertTrue(toString.contains("ActualWork"));
        assertTrue(toString.contains("田中太郎"));
        assertTrue(toString.contains("progress=8"));
        assertTrue(toString.contains("progressRate=80.0"));
    }

    @Test
    void testInheritanceFromBaseEntity() {
        assertTrue(actualWork instanceof BaseEntity);
        
        LocalDateTime now = LocalDateTime.now();
        actualWork.setId(1L);
        actualWork.setCreatedAt(now);
        actualWork.setUpdatedAt(now);
        actualWork.setVersion(1L);
        actualWork.setCreatedBy("admin");
        actualWork.setUpdatedBy("admin");
        actualWork.setDeleted(false);

        assertEquals(1L, actualWork.getId());
        assertEquals(now, actualWork.getCreatedAt());
        assertEquals(now, actualWork.getUpdatedAt());
        assertEquals(1L, actualWork.getVersion());
        assertEquals("admin", actualWork.getCreatedBy());
        assertEquals("admin", actualWork.getUpdatedBy());
        assertFalse(actualWork.isDeleted());
    }
}

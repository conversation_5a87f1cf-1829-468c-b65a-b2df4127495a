import { useEffect, useRef, useState } from 'react'
import { Box, Pagination, Typography, useTheme } from '@mui/material'
import { ElementCard } from '../app/dashboard'
import type { Task } from '../models/Task'
import { fetchTasks } from '../app/dashboard/services/tasks'

const elementsPerPagePortrait = 4
const elementsPerPageLandscape = 1

function isAbortError(err: unknown): boolean {
	if (err instanceof DOMException) return err.name === 'AbortError'
	if (typeof err === 'object' && err !== null && 'name' in err) {
		const name = (err as { name?: unknown }).name
		return typeof name === 'string' && name === 'AbortError'
	}
	return false
}

export default function DashboardPage() {
	const theme = useTheme()
	const [page, setPage] = useState(1)
	const [isLandscape, setIsLandscape] = useState(
		() => matchMedia('(orientation: landscape)').matches,
	)
	const [tasks, setTasks] = useState<Task[]>([])
	const [loading, setLoading] = useState(true)
	const [error, setError] = useState<string | null>(null)

	// Keep a lightweight checksum to avoid re-renders if data is unchanged
	const prevChecksumRef = useRef<string>('')
	const computeChecksum = (list: Task[]) =>
		JSON.stringify(
			list.map((t) => ({
				id: t.id,
				name: t.name,
				progress: t.progress,
				plannedStart: t.plannedStart,
				plannedEnd: t.plannedEnd,
				plannedDuration: t.plannedDuration,
				actualStart: t.actualStart,
				actualEnd: t.actualEnd,
				actualDuration: t.actualDuration,
			})),
		)

	// Visibility tracking
	const observerRef = useRef<IntersectionObserver | null>(null)
	const itemElementsRef = useRef<Map<number, Element>>(new Map())
	const [visibleItems, setVisibleItems] = useState<Set<number>>(new Set())

	useEffect(() => {
		const controller = new AbortController()
		setLoading(true)
		setError(null)
		fetchTasks(controller.signal)
			.then((data) => {
				const checksum = computeChecksum(data)
				if (checksum !== prevChecksumRef.current) {
					prevChecksumRef.current = checksum
					setTasks(data)
					console.log('Fetched tasks:', data);
				}
			})
			.catch((e) => {
				const isAbort = isAbortError(e)
				if (!isAbort) {
					setError(e instanceof Error ? e.message : 'Unknown error')
				}
			})
			.finally(() => setLoading(false))
		return () => controller.abort()
	}, [])

	useEffect(() => {
		const mq = matchMedia('(orientation: landscape)')
		const handler = () => setIsLandscape(mq.matches)
		mq.addEventListener?.('change', handler)
		return () => mq.removeEventListener?.('change', handler)
	}, [])

	// Setup IntersectionObserver once
	useEffect(() => {
		if (observerRef.current) observerRef.current.disconnect()
		observerRef.current = new IntersectionObserver(
			(entries) => {
				setVisibleItems((prev) => {
					const next = new Set(prev)
					for (const entry of entries) {
						const idAttr = entry.target.getAttribute('data-item-id')
						const id = idAttr ? Number(idAttr) : NaN
						if (!Number.isNaN(id)) {
							if (entry.isIntersecting && entry.intersectionRatio > 0) {
								next.add(id)
							} else {
								next.delete(id)
							}
						}
					}
					return next
				})
			},
			{ root: null, threshold: 0.1 },
		)

		// Observe current elements
		for (const el of itemElementsRef.current.values()) {
			observerRef.current.observe(el)
		}
		return () => observerRef.current?.disconnect()
	}, [page])

	// Periodic refresh every 10s only when there is at least one visible item
	useEffect(() => {
		let isCancelled = false
		const intervalId = setInterval(() => {
			if (visibleItems.size === 0) return
			const controller = new AbortController()
			fetchTasks(controller.signal)
				.then((data) => {
					if (isCancelled) return
					const checksum = computeChecksum(data)
					if (checksum !== prevChecksumRef.current) {
						prevChecksumRef.current = checksum
						setTasks(data)
					}
				})
				.catch((e) => {
					if (!isAbortError(e)) {
						// swallow non-abort errors for background refresh but surface could be added if needed
					}
				})
				.finally(() => controller.abort())
		}, 10_000)
		return () => {
			isCancelled = true
			clearInterval(intervalId)
		}
	}, [visibleItems])

	const elementsPerPage = isLandscape ? elementsPerPageLandscape : elementsPerPagePortrait
	const totalElements = 12
	const totalPages = Math.ceil(totalElements / elementsPerPage)
	const startIndex = (page - 1) * elementsPerPage
	const pageItems = Array.from(
		{ length: Math.min(elementsPerPage, totalElements - startIndex) },
		(_, i) => startIndex + i,
	)

	if (loading) {
		return (
			<Box className="p-3 sm:p-4 w-full">
				<Typography>Loading…</Typography>
			</Box>
		)
	}
	if (error) {
		return (
			<Box className="p-3 sm:p-4 w-full">
				<Typography color="error">{error}</Typography>
			</Box>
		)
	}

	// Estimate heights for sticky header & bar to compute scrollable area height
	// Use theme spacing units (8px base) and dynamic viewport height for better mobile behavior
	const headerUnits = 5 // 5 * 8 = 40px
	const stickyBarUnits = 7 // 7 * 8 = 56px
	const verticalPadUnits = 2 // 2 * 8 = 16px
	const totalOffset = theme.spacing(headerUnits + stickyBarUnits + verticalPadUnits)
	const maxHCalc = `calc(100dvh - ${totalOffset})`

	return (
		<Box className="space-y-4 w-full">
			<Box
				className="sticky top-0 z-10"
				sx={{
					backgroundColor: theme.palette.background.paper,
					borderBottom: `1px solid ${theme.palette.divider}`,
					boxShadow:
						theme.palette.mode === 'light'
							? '0 1px 2px rgba(0,0,0,0.04)'
							: '0 1px 2px rgba(0,0,0,0.5)',
					pt: 1,
					pb: 1,
				}}
			>
				<div className="flex justify-center">
					<Pagination
						count={totalPages}
						page={page}
						onChange={(_, p) => setPage(p)}
						color="primary"
					/>
				</div>
			</Box>

			<Box sx={{ overflow: 'auto', maxHeight: maxHCalc, pb: { xs: 3, sm: 4 } }}>
				<div className="grid grid-cols-1 gap-3 sm:gap-4 w-full container mx-auto px-3 sm:px-4">
					{pageItems.map((idx) => (
						<ElementCard
							key={idx}
							className="col-span-1 w-full"
							data-item-id={idx}
							ref={(el) => {
								const map = itemElementsRef.current
								if (!el) {
									const prev = map.get(idx)
									if (prev && observerRef.current)
										observerRef.current.unobserve(prev)
									map.delete(idx)
								} else {
									map.set(idx, el)
									if (observerRef.current) observerRef.current.observe(el)
								}
							}}
							tasks={tasks}
						/>
					))}
				</div>
			</Box>
		</Box>
	)
}

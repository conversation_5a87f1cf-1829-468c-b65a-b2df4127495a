# Multi-stage build for optimized production image
FROM gradle:8.5-jdk17 AS builder

WORKDIR /app
COPY build.gradle settings.gradle gradle.properties ./
COPY src ./src

# Build the application
RUN gradle bootJar --no-daemon

# Production stage
FROM openjdk:17-jre-slim

WORKDIR /app

# Create non-root user for security
RUN groupadd -r vamuser && useradd -r -g vamuser vamuser

# Copy the built JAR from builder stage
COPY --from=builder /app/build/libs/*.jar app.jar

# Create logs directory
RUN mkdir -p /app/logs && chown -R vamuser:vamuser /app

# Switch to non-root user
USER vamuser

# Expose port
EXPOSE 8080

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
  CMD curl -f http://localhost:8080/actuator/health || exit 1

# Run the application
ENTRYPOINT ["java", "-jar", "app.jar"]

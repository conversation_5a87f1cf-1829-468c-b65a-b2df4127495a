import { ENV } from '../config/env'

function buildUrl(url: string): string {
	// If URL is already absolute, return as-is
	if (url.startsWith('http://') || url.startsWith('https://')) {
		return url
	}

	// If we have an API base URL configured, use it
	if (ENV.API_BASE_URL) {
		return `${ENV.API_BASE_URL}${url}`
	}

	// Otherwise, use relative URL (for development with proxy)
	return url
}

export async function httpGet<T>(
	url: string,
	init?: RequestInit & { signal?: AbortSignal },
): Promise<T> {
	const fullUrl = buildUrl(url)
	const res = await fetch(fullUrl, { ...init })
	if (!res.ok) throw new Error(`HTTP ${res.status}`)
	return (await res.json()) as T
}

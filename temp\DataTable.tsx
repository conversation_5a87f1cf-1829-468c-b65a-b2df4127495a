import {
	Paper,
	Table,
	TableBody,
	TableCell,
	TableContainer,
	TableHead,
	TableRow,
	Box,
	IconButton,
	Tooltip,
} from '@mui/material'
import FullscreenIcon from '@mui/icons-material/Fullscreen'
import type { Task } from '../../../models/Task'
import { useTranslation } from 'react-i18next'
import { memo } from 'react'

function rowsChecksum(rows: Task[]): string {
	return JSON.stringify(
		rows.map((r) => ({
			id: r.id,
			no: r.no,
			shppingDate: r.shppingDate,
			vangp: r.vangp,
			deliveryTime: r.deliveryTime,
			plannedStart: r.plannedStart,
			plannedEnd: r.plannedEnd,
			plannedDuration: r.plannedDuration,
			actualStart: r.actualStart,
			actualEnd: r.actualEnd,
			actualDuration: r.actualDuration,
		})),
	)
}

function DataTableComponent({
	rows,
	selectedRowId,
	onSelect,
	onFullView,
}: {
	rows: Task[]
	selectedRowId?: number | null
	onSelect?: (row: Task) => void
	onFullView?: (content: React.ReactNode, title?: string) => void
}) {
	const { t } = useTranslation()

	// Constants for sticky header positioning
	const mainHeaderHeight = 48
	const subHeaderHeight = 48
	const mainHeaderTop = 0
	const subHeaderTop = mainHeaderHeight
	const mainHeaderZIndex = 12
	const subHeaderZIndex = 11

	return (
		<TableContainer
			component={Paper}
			sx={{ maxHeight: { xs: 384, sm: 448, md: 480 }, p: 0, position: 'relative' }}
		>
			<Box className="flex items-center justify-end px-2 py-1">
				{onFullView ? (
					<Tooltip title={t('common.fullscreen') ?? 'Fullscreen'}>
						<IconButton
							size="small"
							aria-label="fullscreen table"
							onClick={() =>
								onFullView(
									<Box sx={{ height: 'calc(100dvh - 120px)', overflow: 'auto' }}>
										<Table stickyHeader size="small">
											{/* Reuse same header and body for fullscreen view */}
											<TableHead>
												<TableRow sx={{ height: 48 }}>
													<TableCell
														colSpan={4}
														align="center"
														sx={{ fontWeight: 600 }}
													>
														{t('table.name')}
													</TableCell>
													<TableCell
														colSpan={3}
														align="center"
														sx={{ fontWeight: 600 }}
													>
														{t('table.planned')}
													</TableCell>
													<TableCell
														colSpan={3}
														align="center"
														sx={{ fontWeight: 600 }}
													>
														{t('table.actual')}
													</TableCell>
												</TableRow>
												<TableRow sx={{ height: 48 }}>
													<TableCell align="center">
														{t('table.no')}
													</TableCell>
													<TableCell align="center">
														{t('table.shippingDate')}
													</TableCell>
													<TableCell align="center">
														{t('table.vangp')}
													</TableCell>
													<TableCell align="center">
														{t('table.deliveryTime')}
													</TableCell>
													<TableCell align="center">
														{t('table.start')}
													</TableCell>
													<TableCell align="center">
														{t('table.end')}
													</TableCell>
													<TableCell align="center">
														{t('table.duration')}
													</TableCell>
													<TableCell align="center">
														{t('table.start')}
													</TableCell>
													<TableCell align="center">
														{t('table.end')}
													</TableCell>
													<TableCell align="center">
														{t('table.duration')}
													</TableCell>
												</TableRow>
											</TableHead>
											<TableBody>
												{rows.map((row, index) => (
													<TableRow key={`${row.id}-fs-${index}`} hover>
														<TableCell align="center">
															{row.no}
														</TableCell>
														<TableCell align="center">
															{row.shppingDate}
														</TableCell>
														<TableCell align="center">
															{row.vangp}
														</TableCell>
														<TableCell align="center">
															{row.deliveryTime}
														</TableCell>
														<TableCell align="center">
															{row.plannedStart}
														</TableCell>
														<TableCell align="center">
															{row.plannedEnd}
														</TableCell>
														<TableCell align="center">
															{row.plannedDuration}
														</TableCell>
														<TableCell align="center">
															{row.actualStart}
														</TableCell>
														<TableCell align="center">
															{row.actualEnd}
														</TableCell>
														<TableCell align="center">
															{row.actualDuration}
														</TableCell>
													</TableRow>
												))}
											</TableBody>
										</Table>
									</Box>,
									'Table',
								)
							}
						>
						<FullscreenIcon fontSize="small" />
						</IconButton>
					</Tooltip>
				) : null}
			</Box>
			<Table stickyHeader size="small">
				<TableHead>
					{/* Top-level headers */}
					<TableRow sx={{ height: mainHeaderHeight }}>
						<TableCell
							colSpan={4}
							align="center"
							sx={{
								position: 'sticky',
								top: mainHeaderTop,
								zIndex: mainHeaderZIndex,
								backgroundColor: 'background.paper',
								borderBottom: '1px solid',
								borderColor: 'divider',
								fontWeight: 600,
							}}
						>
							{t('table.name')}
						</TableCell>
						<TableCell
							colSpan={3}
							align="center"
							sx={{
								position: 'sticky',
								top: mainHeaderTop,
								zIndex: mainHeaderZIndex,
								backgroundColor: 'background.paper',
								borderBottom: '1px solid',
								borderColor: 'divider',
								fontWeight: 600,
							}}
						>
							{t('table.planned')}
						</TableCell>
						<TableCell
							colSpan={3}
							align="center"
							sx={{
								position: 'sticky',
								top: mainHeaderTop,
								zIndex: mainHeaderZIndex,
								backgroundColor: 'background.paper',
								borderBottom: '1px solid',
								borderColor: 'divider',
								fontWeight: 600,
							}}
						>
							{t('table.actual')}
						</TableCell>
					</TableRow>
					{/* Sub-headers */}
					<TableRow sx={{ height: subHeaderHeight }}>
						<TableCell
							align="center"
							sx={{
								position: 'sticky',
								top: subHeaderTop,
								zIndex: subHeaderZIndex,
								backgroundColor: 'background.paper',
								borderBottom: '1px solid',
								borderColor: 'divider',
								fontWeight: 500,
							}}
						>
							{t('table.no')}
						</TableCell>
						<TableCell
							align="center"
							sx={{
								position: 'sticky',
								top: subHeaderTop,
								zIndex: subHeaderZIndex,
								backgroundColor: 'background.paper',
								borderBottom: '1px solid',
								borderColor: 'divider',
								fontWeight: 500,
							}}
						>
							{t('table.shippingDate')}
						</TableCell>
						<TableCell
							align="center"
							sx={{
								position: 'sticky',
								top: subHeaderTop,
								zIndex: subHeaderZIndex,
								backgroundColor: 'background.paper',
								borderBottom: '1px solid',
								borderColor: 'divider',
								fontWeight: 500,
							}}
						>
							{t('table.vangp')}
						</TableCell>
						<TableCell
							align="center"
							sx={{
								position: 'sticky',
								top: subHeaderTop,
								zIndex: subHeaderZIndex,
								backgroundColor: 'background.paper',
								borderBottom: '1px solid',
								borderColor: 'divider',
								fontWeight: 500,
							}}
						>
							{t('table.deliveryTime')}
						</TableCell>
						<TableCell
							align="center"
							sx={{
								position: 'sticky',
								top: subHeaderTop,
								zIndex: subHeaderZIndex,
								backgroundColor: 'background.paper',
								borderBottom: '1px solid',
								borderColor: 'divider',
								fontWeight: 500,
							}}
						>
							{t('table.start')}
						</TableCell>
						<TableCell
							align="center"
							sx={{
								position: 'sticky',
								top: subHeaderTop,
								zIndex: subHeaderZIndex,
								backgroundColor: 'background.paper',
								borderBottom: '1px solid',
								borderColor: 'divider',
								fontWeight: 500,
							}}
						>
							{t('table.end')}
						</TableCell>
						<TableCell
							align="center"
							sx={{
								position: 'sticky',
								top: subHeaderTop,
								zIndex: subHeaderZIndex,
								backgroundColor: 'background.paper',
								borderBottom: '1px solid',
								borderColor: 'divider',
								fontWeight: 500,
							}}
						>
							{t('table.duration')}
						</TableCell>
						<TableCell
							align="center"
							sx={{
								position: 'sticky',
								top: subHeaderTop,
								zIndex: subHeaderZIndex,
								backgroundColor: 'background.paper',
								borderBottom: '1px solid',
								borderColor: 'divider',
								fontWeight: 500,
							}}
						>
							{t('table.start')}
						</TableCell>
						<TableCell
							align="center"
							sx={{
								position: 'sticky',
								top: subHeaderTop,
								zIndex: subHeaderZIndex,
								backgroundColor: 'background.paper',
								borderBottom: '1px solid',
								borderColor: 'divider',
								fontWeight: 500,
							}}
						>
							{t('table.end')}
						</TableCell>
						<TableCell
							align="center"
							sx={{
								position: 'sticky',
								top: subHeaderTop,
								zIndex: subHeaderZIndex,
								backgroundColor: 'background.paper',
								borderBottom: '1px solid',
								borderColor: 'divider',
								fontWeight: 500,
							}}
						>
							{t('table.duration')}
						</TableCell>
					</TableRow>
				</TableHead>
				<TableBody sx={{ '& td': { backgroundColor: 'background.default' } }}>
					{rows.map((row, index) => {
						const isSelected = row.id === selectedRowId
						return (
							<TableRow
								key={`${row.id}-${index}`}
								hover
								selected={isSelected}
								onClick={() => onSelect?.(row)}
								sx={{ cursor: onSelect ? 'pointer' : 'default' }}
								aria-selected={isSelected}
							>
								<TableCell align="center">{row.no}</TableCell>
								<TableCell align="center">{row.shppingDate}</TableCell>
								<TableCell align="center">{row.vangp}</TableCell>
								<TableCell align="center">{row.deliveryTime}</TableCell>
								<TableCell align="center">{row.plannedStart}</TableCell>
								<TableCell align="center">{row.plannedEnd}</TableCell>
								<TableCell align="center">{row.plannedDuration}</TableCell>
								<TableCell align="center">{row.actualStart}</TableCell>
								<TableCell align="center">{row.actualEnd}</TableCell>
								<TableCell align="center">{row.actualDuration}</TableCell>
							</TableRow>
						)
					})}
				</TableBody>
			</Table>
		</TableContainer>
	)
}

const DataTable = memo(
	DataTableComponent,
	(prev, next) =>
		prev.selectedRowId === next.selectedRowId &&
		rowsChecksum(prev.rows) === rowsChecksum(next.rows),
)

export default DataTable

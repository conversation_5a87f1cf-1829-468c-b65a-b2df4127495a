<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.inspeedia.vanning.validation.ValidationTest" tests="11" skipped="0" failures="0" errors="0" timestamp="2025-09-01T12:10:48" hostname="MSI" time="0.493">
  <properties/>
  <testcase name="testInvalidActualWorkProgressOutOfRange()" classname="com.inspeedia.vanning.validation.ValidationTest" time="0.188"/>
  <testcase name="testInvalidPlannedWorkVanGpWrongPattern()" classname="com.inspeedia.vanning.validation.ValidationTest" time="0.042"/>
  <testcase name="testValidPlannedWork()" classname="com.inspeedia.vanning.validation.ValidationTest" time="0.025"/>
  <testcase name="testInvalidPlannedWorkVanGpWrongLength()" classname="com.inspeedia.vanning.validation.ValidationTest" time="0.059"/>
  <testcase name="testInvalidActualWorkProgressRateOutOfRange()" classname="com.inspeedia.vanning.validation.ValidationTest" time="0.022"/>
  <testcase name="testInvalidPlannedWorkPlatformWrongLength()" classname="com.inspeedia.vanning.validation.ValidationTest" time="0.043"/>
  <testcase name="testInvalidActualWorkBlankUserName()" classname="com.inspeedia.vanning.validation.ValidationTest" time="0.019"/>
  <testcase name="testValidActualWork()" classname="com.inspeedia.vanning.validation.ValidationTest" time="0.024"/>
  <testcase name="testActualWorkUserNameTooLong()" classname="com.inspeedia.vanning.validation.ValidationTest" time="0.03"/>
  <testcase name="testInvalidPlannedWorkSizeWrongLength()" classname="com.inspeedia.vanning.validation.ValidationTest" time="0.017"/>
  <testcase name="testMultipleValidationErrors()" classname="com.inspeedia.vanning.validation.ValidationTest" time="0.017"/>
  <system-out><![CDATA[]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>

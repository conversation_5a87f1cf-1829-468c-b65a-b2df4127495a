<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.inspeedia.vanning.validation.ValidationTest" tests="11" skipped="0" failures="0" errors="0" timestamp="2025-09-02T05:14:34" hostname="MSI" time="0.736">
  <properties/>
  <testcase name="testInvalidActualWorkProgressOutOfRange()" classname="com.inspeedia.vanning.validation.ValidationTest" time="0.442"/>
  <testcase name="testInvalidPlannedWorkVanGpWrongPattern()" classname="com.inspeedia.vanning.validation.ValidationTest" time="0.042"/>
  <testcase name="testValidPlannedWork()" classname="com.inspeedia.vanning.validation.ValidationTest" time="0.023"/>
  <testcase name="testInvalidPlannedWorkVanGpWrongLength()" classname="com.inspeedia.vanning.validation.ValidationTest" time="0.028"/>
  <testcase name="testInvalidActualWorkProgressRateOutOfRange()" classname="com.inspeedia.vanning.validation.ValidationTest" time="0.02"/>
  <testcase name="testInvalidPlannedWorkPlatformWrongLength()" classname="com.inspeedia.vanning.validation.ValidationTest" time="0.055"/>
  <testcase name="testInvalidActualWorkBlankUserName()" classname="com.inspeedia.vanning.validation.ValidationTest" time="0.021"/>
  <testcase name="testValidActualWork()" classname="com.inspeedia.vanning.validation.ValidationTest" time="0.018"/>
  <testcase name="testActualWorkUserNameTooLong()" classname="com.inspeedia.vanning.validation.ValidationTest" time="0.026"/>
  <testcase name="testInvalidPlannedWorkSizeWrongLength()" classname="com.inspeedia.vanning.validation.ValidationTest" time="0.029"/>
  <testcase name="testMultipleValidationErrors()" classname="com.inspeedia.vanning.validation.ValidationTest" time="0.028"/>
  <system-out><![CDATA[]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>

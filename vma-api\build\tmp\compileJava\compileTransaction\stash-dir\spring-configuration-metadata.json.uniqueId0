{"groups": [{"name": "app", "type": "com.inspeedia.vanning.config.AppProperties", "sourceType": "com.inspeedia.vanning.config.AppProperties"}, {"name": "app.api", "type": "com.inspeedia.vanning.config.AppProperties$Api", "sourceType": "com.inspeedia.vanning.config.AppProperties", "sourceMethod": "getApi()"}, {"name": "app.cors", "type": "com.inspeedia.vanning.config.AppProperties$Cors", "sourceType": "com.inspeedia.vanning.config.AppProperties", "sourceMethod": "getCors()"}, {"name": "app.pagination", "type": "com.inspeedia.vanning.config.AppProperties$Pagination", "sourceType": "com.inspeedia.vanning.config.AppProperties", "sourceMethod": "getPagination()"}], "properties": [{"name": "app.api.base-path", "type": "java.lang.String", "sourceType": "com.inspeedia.vanning.config.AppProperties$Api"}, {"name": "app.api.version", "type": "java.lang.String", "sourceType": "com.inspeedia.vanning.config.AppProperties$Api"}, {"name": "app.cors.allow-credentials", "type": "java.lang.Bo<PERSON>an", "sourceType": "com.inspeedia.vanning.config.AppProperties$Cors"}, {"name": "app.cors.allowed-headers", "type": "java.util.List<java.lang.String>", "sourceType": "com.inspeedia.vanning.config.AppProperties$Cors"}, {"name": "app.cors.allowed-methods", "type": "java.util.List<java.lang.String>", "sourceType": "com.inspeedia.vanning.config.AppProperties$Cors"}, {"name": "app.cors.allowed-origins", "type": "java.util.List<java.lang.String>", "sourceType": "com.inspeedia.vanning.config.AppProperties$Cors"}, {"name": "app.cors.max-age", "type": "java.lang.Long", "sourceType": "com.inspeedia.vanning.config.AppProperties$Cors"}, {"name": "app.pagination.default-page-size", "type": "java.lang.Integer", "sourceType": "com.inspeedia.vanning.config.AppProperties$Pagination"}, {"name": "app.pagination.max-page-size", "type": "java.lang.Integer", "sourceType": "com.inspeedia.vanning.config.AppProperties$Pagination"}], "hints": [], "ignored": {"properties": []}}
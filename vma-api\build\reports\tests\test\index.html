<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<meta http-equiv="x-ua-compatible" content="IE=edge"/>
<title>Test results - Test Summary</title>
<link href="css/base-style.css" rel="stylesheet" type="text/css"/>
<link href="css/style.css" rel="stylesheet" type="text/css"/>
<script src="js/report.js" type="text/javascript"></script>
</head>
<body>
<div id="content">
<h1>Test Summary</h1>
<div id="summary">
<table>
<tr>
<td>
<div class="summaryGroup">
<table>
<tr>
<td>
<div class="infoBox" id="tests">
<div class="counter">98</div>
<p>tests</p>
</div>
</td>
<td>
<div class="infoBox" id="failures">
<div class="counter">0</div>
<p>failures</p>
</div>
</td>
<td>
<div class="infoBox" id="ignored">
<div class="counter">0</div>
<p>ignored</p>
</div>
</td>
<td>
<div class="infoBox" id="duration">
<div class="counter">4.251s</div>
<p>duration</p>
</div>
</td>
</tr>
</table>
</div>
</td>
<td>
<div class="infoBox success" id="successRate">
<div class="percent">100%</div>
<p>successful</p>
</div>
</td>
</tr>
</table>
</div>
<div id="tabs">
<ul class="tabLinks">
<li>
<a href="#tab0">Packages</a>
</li>
<li>
<a href="#tab1">Classes</a>
</li>
</ul>
<div id="tab0" class="tab">
<h2>Packages</h2>
<table>
<thead>
<tr>
<th>Package</th>
<th>Tests</th>
<th>Failures</th>
<th>Ignored</th>
<th>Duration</th>
<th>Success rate</th>
</tr>
</thead>
<tbody>
<tr>
<td class="success">
<a href="packages/com.inspeedia.vanning.html">com.inspeedia.vanning</a>
</td>
<td>2</td>
<td>0</td>
<td>0</td>
<td>1.075s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="packages/com.inspeedia.vanning.config.html">com.inspeedia.vanning.config</a>
</td>
<td>14</td>
<td>0</td>
<td>0</td>
<td>0.093s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="packages/com.inspeedia.vanning.controller.html">com.inspeedia.vanning.controller</a>
</td>
<td>4</td>
<td>0</td>
<td>0</td>
<td>0.891s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="packages/com.inspeedia.vanning.domain.html">com.inspeedia.vanning.domain</a>
</td>
<td>22</td>
<td>0</td>
<td>0</td>
<td>0.128s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="packages/com.inspeedia.vanning.exception.html">com.inspeedia.vanning.exception</a>
</td>
<td>13</td>
<td>0</td>
<td>0</td>
<td>0.053s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="packages/com.inspeedia.vanning.service.html">com.inspeedia.vanning.service</a>
</td>
<td>32</td>
<td>0</td>
<td>0</td>
<td>1.279s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="packages/com.inspeedia.vanning.validation.html">com.inspeedia.vanning.validation</a>
</td>
<td>11</td>
<td>0</td>
<td>0</td>
<td>0.732s</td>
<td class="success">100%</td>
</tr>
</tbody>
</table>
</div>
<div id="tab1" class="tab">
<h2>Classes</h2>
<table>
<thead>
<tr>
<th>Class</th>
<th>Tests</th>
<th>Failures</th>
<th>Ignored</th>
<th>Duration</th>
<th>Success rate</th>
</tr>
</thead>
<tbody>
<tr>
<td class="success">
<a href="classes/com.inspeedia.vanning.ApplicationStartupTest.html">com.inspeedia.vanning.ApplicationStartupTest</a>
</td>
<td>1</td>
<td>0</td>
<td>0</td>
<td>1.068s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/com.inspeedia.vanning.VmApplicationTests.html">com.inspeedia.vanning.VmApplicationTests</a>
</td>
<td>1</td>
<td>0</td>
<td>0</td>
<td>0.007s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/com.inspeedia.vanning.config.AppPropertiesTest.html">com.inspeedia.vanning.config.AppPropertiesTest</a>
</td>
<td>14</td>
<td>0</td>
<td>0</td>
<td>0.093s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/com.inspeedia.vanning.controller.TaskControllerTest.html">com.inspeedia.vanning.controller.TaskControllerTest</a>
</td>
<td>4</td>
<td>0</td>
<td>0</td>
<td>0.891s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/com.inspeedia.vanning.domain.ActualWorkTest.html">com.inspeedia.vanning.domain.ActualWorkTest</a>
</td>
<td>8</td>
<td>0</td>
<td>0</td>
<td>0.072s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/com.inspeedia.vanning.domain.BaseEntityTest.html">com.inspeedia.vanning.domain.BaseEntityTest</a>
</td>
<td>6</td>
<td>0</td>
<td>0</td>
<td>0.012s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/com.inspeedia.vanning.domain.PlannedWorkTest.html">com.inspeedia.vanning.domain.PlannedWorkTest</a>
</td>
<td>8</td>
<td>0</td>
<td>0</td>
<td>0.044s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/com.inspeedia.vanning.exception.ErrorResponseTest.html">com.inspeedia.vanning.exception.ErrorResponseTest</a>
</td>
<td>13</td>
<td>0</td>
<td>0</td>
<td>0.053s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/com.inspeedia.vanning.service.ActualWorkServiceTest.html">com.inspeedia.vanning.service.ActualWorkServiceTest</a>
</td>
<td>14</td>
<td>0</td>
<td>0</td>
<td>0.848s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/com.inspeedia.vanning.service.PlannedWorkServiceTest.html">com.inspeedia.vanning.service.PlannedWorkServiceTest</a>
</td>
<td>14</td>
<td>0</td>
<td>0</td>
<td>0.191s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/com.inspeedia.vanning.service.TaskServiceTest.html">com.inspeedia.vanning.service.TaskServiceTest</a>
</td>
<td>4</td>
<td>0</td>
<td>0</td>
<td>0.240s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/com.inspeedia.vanning.validation.ValidationTest.html">com.inspeedia.vanning.validation.ValidationTest</a>
</td>
<td>11</td>
<td>0</td>
<td>0</td>
<td>0.732s</td>
<td class="success">100%</td>
</tr>
</tbody>
</table>
</div>
</div>
<div id="footer">
<p>
<div>
<label class="hidden" id="label-for-line-wrapping-toggle" for="line-wrapping-toggle">Wrap lines
<input id="line-wrapping-toggle" type="checkbox" autocomplete="off"/>
</label>
</div>Generated by 
<a href="http://www.gradle.org">Gradle 8.5</a> at Sep 2, 2025, 10:44:35 AM</p>
</div>
</div>
</body>
</html>

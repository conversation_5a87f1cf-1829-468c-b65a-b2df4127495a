import { lazy } from 'react'
import RootLayout from '../layout'

const DashboardPage = lazy(() => import('../../pages/DashboardPage'))
const SettingsPage = lazy(() => import('../../pages/SettingsPage'))

export const routes = [
    {
        path: '/',
        element: <RootLayout />,
        children: [
            { index: true, element: <DashboardPage /> },
            { path: 'settings', element: <SettingsPage /> },
        ],
    },
]

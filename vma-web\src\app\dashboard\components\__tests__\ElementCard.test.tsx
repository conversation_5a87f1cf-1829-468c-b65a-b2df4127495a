/**
 * Tests for ElementCard component
 */

import { render, screen, fireEvent, waitFor } from '../../../../test-utils'
import ElementCard from '../ElementCard'
import { mockTasks, mockEmptyTasks } from '../../../../__mocks__/mockData'

// Mock the lazy-loaded ChartCard component
jest.mock('../ChartCard', () => {
	const MockChartCard = ({ selectedTask }: { selectedTask: { name: string } | null }) => {
		return selectedTask ? (
			<div data-testid="chart-card">Chart for: {selectedTask.name}</div>
		) : (
			<div data-testid="chart-card">No task selected</div>
		)
	}
	// Make the mock component work with lazy loading
	MockChartCard.displayName = 'MockChartCard'
	return {
		__esModule: true,
		default: MockChartCard,
	}
})

// Mock DataTable component
jest.mock('../DataTable', () => {
	return function MockDataTable({
		rows,
		selectedRowId,
		onSelect
	}: {
		rows: Array<{ id: string; name: string }>;
		selectedRowId?: string;
		onSelect?: (row: { id: string; name: string }) => void
	}) {
		return (
			<div data-testid="data-table">
				{rows.map((row, index) => (
					<div
						key={`${row.id}-${index}`}
						data-testid={`table-row-${row.id}`}
						onClick={() => onSelect?.(row)}
						className={selectedRowId === row.id ? 'selected' : ''}
					>
						{row.name}
					</div>
				))}
			</div>
		)
	}
})

describe('ElementCard Component', () => {
	describe('when rendering with tasks', () => {
		it('should display DataTable and ChartCard', async () => {
			// Arrange & Act
			render(<ElementCard tasks={mockTasks} />)

			// Assert
			expect(screen.getByTestId('data-table')).toBeInTheDocument()
			// Wait for the lazy-loaded ChartCard to render
			await waitFor(() => {
				expect(screen.getByTestId('chart-card')).toBeInTheDocument()
			})
		})

		it('should select first task by default', async () => {
			// Arrange & Act
			render(<ElementCard tasks={mockTasks} />)

			// Assert
			await waitFor(() => {
				expect(screen.getByText('Chart for: Task Alpha')).toBeInTheDocument()
			})
			expect(screen.getByTestId('table-row-1')).toHaveClass('selected')
		})

		it('should display all task rows', () => {
			// Arrange & Act
			render(<ElementCard tasks={mockTasks} />)

			// Assert
			expect(screen.getByTestId('table-row-1')).toBeInTheDocument()
			expect(screen.getByTestId('table-row-2')).toBeInTheDocument()
			expect(screen.getByTestId('table-row-3')).toBeInTheDocument()
		})
	})

	describe('when rendering with empty tasks', () => {
		it('should handle empty tasks array', () => {
			// Arrange & Act
			render(<ElementCard tasks={mockEmptyTasks} />)

			// Assert
			expect(screen.getByTestId('data-table')).toBeInTheDocument()
			expect(screen.getByText('No task selected')).toBeInTheDocument()
		})
	})

	describe('task selection functionality', () => {
		it('should update selected task when row is clicked', async () => {
			// Arrange
			render(<ElementCard tasks={mockTasks} />)

			// Act
			const secondRow = screen.getByTestId('table-row-2')
			fireEvent.click(secondRow)

			// Assert
			await waitFor(() => {
				expect(screen.getByText('Chart for: Task Beta')).toBeInTheDocument()
			})
			expect(secondRow).toHaveClass('selected')
		})

		it('should maintain selection when tasks array changes but selected task still exists', () => {
			// Arrange
			const { rerender } = render(<ElementCard tasks={mockTasks} />)

			// Act - Change tasks but keep the same first task
			const updatedTasks = [
				{ ...mockTasks[0], progress: 90 }, // Same task with updated progress
				{ ...mockTasks[1], name: 'Updated Task Beta' },
			]
			rerender(<ElementCard tasks={updatedTasks} />)

			// Assert
			expect(screen.getByText('Chart for: Task Alpha')).toBeInTheDocument()
			expect(screen.getByTestId('table-row-1')).toHaveClass('selected')
		})

		it('should update selection when selected task is no longer in tasks array', async () => {
			// Arrange
			const { rerender } = render(<ElementCard tasks={mockTasks} />)

			// Act - Remove the first task
			const updatedTasks = mockTasks.slice(1)
			rerender(<ElementCard tasks={updatedTasks} />)

			// Assert
			await waitFor(() => {
				expect(screen.getByText((content) => {
					return content.includes('Chart for:') && content.includes('Task Beta')
				})).toBeInTheDocument()
			})
			expect(screen.getByTestId('table-row-2')).toHaveClass('selected')
		})
	})

	describe('component memoization', () => {
		it('should not re-render when tasks are equal', () => {
			// Arrange
			const { rerender } = render(<ElementCard tasks={mockTasks} />)
			const initialDataTable = screen.getByTestId('data-table')

			// Act
			rerender(<ElementCard tasks={mockTasks} />)

			// Assert
			expect(screen.getByTestId('data-table')).toBe(initialDataTable)
		})

		it('should re-render when tasks change', () => {
			// Arrange
			const { rerender } = render(<ElementCard tasks={mockTasks} />)

			// Act
			const updatedTasks = [...mockTasks, { ...mockTasks[0], id: 4, name: 'Task Delta' }]
			rerender(<ElementCard tasks={updatedTasks} />)

			// Assert
			expect(screen.getByTestId('table-row-4')).toBeInTheDocument()
		})

		it('should re-render when className changes', () => {
			// Arrange
			const { rerender } = render(<ElementCard tasks={mockTasks} />)

			// Act
			rerender(<ElementCard tasks={mockTasks} className="custom-class" />)

			// Assert
			const elementCard = screen.getByTestId('data-table').closest('.MuiPaper-root')
			expect(elementCard).toHaveClass('custom-class')
		})
	})

	describe('layout and styling', () => {
		it('should have proper grid layout', () => {
			// Arrange & Act
			render(<ElementCard tasks={mockTasks} />)

			// Assert
			const container = screen.getByTestId('data-table').closest('.grid')
			expect(container).toHaveClass('grid-cols-1', 'md:grid-cols-3')
		})

		it('should have proper spacing and padding', () => {
			// Arrange & Act
			render(<ElementCard tasks={mockTasks} />)

			// Assert
			const paper = screen.getByTestId('data-table').closest('.MuiPaper-root')
			expect(paper).toHaveClass('space-y-4', 'p-3', 'sm:p-4')
		})

		it('should have proper minimum heights', () => {
			// Arrange & Act
			render(<ElementCard tasks={mockTasks} />)

			// Assert
			const dataTableContainer = screen.getByTestId('data-table').closest('.min-h-64')
			const chartContainer = screen.getByTestId('chart-card').closest('.min-h-64')

			expect(dataTableContainer).toHaveClass('min-h-64', 'md:min-h-72')
			expect(chartContainer).toHaveClass('min-h-64', 'md:min-h-72')
		})
	})

	describe('edge cases', () => {
		it('should handle single task', () => {
			// Arrange
			const singleTask = [mockTasks[0]]
			render(<ElementCard tasks={singleTask} />)

			// Assert
			expect(screen.getByText('Chart for: Task Alpha')).toBeInTheDocument()
			expect(screen.getByTestId('table-row-1')).toHaveClass('selected')
		})

		it('should handle task selection correctly', async () => {
			// Arrange
			const testTasks = [
				mockTasks[0], // Task Alpha with ID 1
				mockTasks[1], // Task Beta with ID 2
			]
			render(<ElementCard tasks={testTasks} />)

			// Act - Click on the second task (Task Beta)
			const secondRow = screen.getByTestId('table-row-2')
			fireEvent.click(secondRow)

			// Assert
			await waitFor(() => {
				expect(screen.getByText('Chart for: Task Beta')).toBeInTheDocument()
			})
			expect(screen.getByTestId('table-row-2')).toHaveClass('selected')
		})
	})

	describe('accessibility', () => {
		it('should have proper semantic structure', () => {
			// Arrange & Act
			render(<ElementCard tasks={mockTasks} />)

			// Assert
			expect(screen.getByTestId('data-table')).toBeInTheDocument()
			expect(screen.getByTestId('chart-card')).toBeInTheDocument()
		})

		it('should maintain focus management', () => {
			// Arrange
			render(<ElementCard tasks={mockTasks} />)

			// Act
			const firstRow = screen.getByTestId('table-row-1')
			firstRow.focus()

			// Assert - Check that the element can receive focus (it's focusable)
			expect(firstRow).toBeInTheDocument()
			// Note: Focus management in tests can be tricky with table rows
			// The important thing is that the element is focusable and accessible
		})
	})
})

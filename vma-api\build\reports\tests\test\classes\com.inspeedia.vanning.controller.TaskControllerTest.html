<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<meta http-equiv="x-ua-compatible" content="IE=edge"/>
<title>Test results - TaskControllerTest</title>
<link href="../css/base-style.css" rel="stylesheet" type="text/css"/>
<link href="../css/style.css" rel="stylesheet" type="text/css"/>
<script src="../js/report.js" type="text/javascript"></script>
</head>
<body>
<div id="content">
<h1>TaskControllerTest</h1>
<div class="breadcrumbs">
<a href="../index.html">all</a> &gt; 
<a href="../packages/com.inspeedia.vanning.controller.html">com.inspeedia.vanning.controller</a> &gt; TaskControllerTest</div>
<div id="summary">
<table>
<tr>
<td>
<div class="summaryGroup">
<table>
<tr>
<td>
<div class="infoBox" id="tests">
<div class="counter">4</div>
<p>tests</p>
</div>
</td>
<td>
<div class="infoBox" id="failures">
<div class="counter">0</div>
<p>failures</p>
</div>
</td>
<td>
<div class="infoBox" id="ignored">
<div class="counter">0</div>
<p>ignored</p>
</div>
</td>
<td>
<div class="infoBox" id="duration">
<div class="counter">0.891s</div>
<p>duration</p>
</div>
</td>
</tr>
</table>
</div>
</td>
<td>
<div class="infoBox success" id="successRate">
<div class="percent">100%</div>
<p>successful</p>
</div>
</td>
</tr>
</table>
</div>
<div id="tabs">
<ul class="tabLinks">
<li>
<a href="#tab0">Tests</a>
</li>
<li>
<a href="#tab1">Standard output</a>
</li>
</ul>
<div id="tab0" class="tab">
<h2>Tests</h2>
<table>
<thead>
<tr>
<th>Test</th>
<th>Duration</th>
<th>Result</th>
</tr>
</thead>
<tr>
<td class="success">testGetAllTasks()</td>
<td class="success">0.853s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testGetAllTasksEmpty()</td>
<td class="success">0.022s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testGetTodaysTasks()</td>
<td class="success">0.010s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testGetTodaysTasksEmpty()</td>
<td class="success">0.006s</td>
<td class="success">passed</td>
</tr>
</table>
</div>
<div id="tab1" class="tab">
<h2>Standard output</h2>
<span class="code">
<pre>{&quot;@timestamp&quot;:&quot;2025-09-02T10:44:32.5709754+05:30&quot;,&quot;level&quot;:&quot;INFO&quot;,&quot;logger_name&quot;:&quot;com.inspeedia.vanning.controller.TaskController&quot;,&quot;message&quot;:&quot;Fetching all tasks&quot;}
{&quot;@timestamp&quot;:&quot;2025-09-02T10:44:32.8600602+05:30&quot;,&quot;level&quot;:&quot;INFO&quot;,&quot;logger_name&quot;:&quot;com.inspeedia.vanning.controller.TaskController&quot;,&quot;message&quot;:&quot;Fetching all tasks&quot;}
{&quot;@timestamp&quot;:&quot;2025-09-02T10:44:32.880148+05:30&quot;,&quot;level&quot;:&quot;INFO&quot;,&quot;logger_name&quot;:&quot;com.inspeedia.vanning.controller.TaskController&quot;,&quot;message&quot;:&quot;Fetching today's tasks&quot;}
{&quot;@timestamp&quot;:&quot;2025-09-02T10:44:32.8909853+05:30&quot;,&quot;level&quot;:&quot;INFO&quot;,&quot;logger_name&quot;:&quot;com.inspeedia.vanning.controller.TaskController&quot;,&quot;message&quot;:&quot;Fetching today's tasks&quot;}
</pre>
</span>
</div>
</div>
<div id="footer">
<p>
<div>
<label class="hidden" id="label-for-line-wrapping-toggle" for="line-wrapping-toggle">Wrap lines
<input id="line-wrapping-toggle" type="checkbox" autocomplete="off"/>
</label>
</div>Generated by 
<a href="http://www.gradle.org">Gradle 8.5</a> at Sep 2, 2025, 10:44:35 AM</p>
</div>
</div>
</body>
</html>
